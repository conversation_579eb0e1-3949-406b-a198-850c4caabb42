// workingDocumentProcessor.ts - Complete Fixed Version with Multi-Document Support, AI Model Routing, and Selective Check System

import { CheckResult } from './checkDefinitions';
import { processDocumentCheckWithRouting, logRoutingConfiguration } from './modelRouter';
import { processAllMultiDocumentChecks, getAvailableMultiDocumentChecks } from './enhancedInterlinkedProcessor';

export interface ProcessingProgress {
  completed: number;
  total: number;
  currentCheck: string;
  phase: 'single-document' | 'multi-document' | 'complete';
}

export interface DocumentFiles {
  audit_report?: File;
  annexure_a?: File;
  annexure_b?: File;
  balance_sheet?: File;
  notes?: File;
  pl_notes?: File;
  annual_report?: File;
  sec_report?: File;
  csr_notes?: File;
  secretarial_compliance?: File;
}

export interface AnalysisParameters {
  company_name: string;
  audit_date: Date;
  profit_or_loss: string;
  company_listing_status: string;
  top_1000_or_500: string;
  audit_report_type: string;
  audit_opinion_type: string;
  is_nbfc: string;
  has_internal_auditor: string;
  has_cost_auditor: string;
  related_party_note_number: string;
}

// ===================================================================
// INTERFACES FOR SELECTIVE CHECK SYSTEM
// ===================================================================

export interface CheckOption {
  id: string;
  name: string;
  description: string;
  category: string;
  documentTypes: string[];
  estimatedTime: string; // e.g., "30 seconds"
  isAvailable: boolean; // based on uploaded documents and parameters
  isSelected: boolean; // user's choice
}

export interface CheckCategory {
  name: string;
  checks: CheckOption[];
  totalSelected: number;
  totalAvailable: number;
}

/**
 * Simple document validation
 */
export function validateDocuments(documents: DocumentFiles): {
  isValid: boolean;
  errors: string[];
  warnings: string[];
} {
  const errors: string[] = [];
  const warnings: string[] = [];
  
  // Check if at least ONE document is provided (any document)
  const uploadedDocuments = Object.values(documents).filter(file => file !== undefined);
  if (uploadedDocuments.length === 0) {
    errors.push('At least one document is required for analysis');
  }
  
  // Check file types and sizes
  for (const [docType, file] of Object.entries(documents)) {
    if (file) {
      if (file.type !== 'application/pdf') {
        errors.push(`${docType} must be a PDF file (got ${file.type})`);
      }
      
      if (file.size === 0) {
        errors.push(`${docType} file is empty`);
      }
      
      if (file.size > 50 * 1024 * 1024) { // 50MB limit
        warnings.push(`${docType} file is very large (${Math.round(file.size / 1024 / 1024)}MB). Processing may be slow.`);
      }
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors,
    warnings
  };
}

/**
 * Get available checks for selection UI
 */
export function getAvailableChecksForSelection(
  documents: DocumentFiles,
  parameters: AnalysisParameters
): CheckOption[] {
  const availableChecks: CheckOption[] = [];
  
  // AUDIT REPORT CHECKS
  if (documents.audit_report) {
    availableChecks.push(
      {
        id: 'audit_title',
        name: 'Independent Auditor\'s Report Title',
        description: 'Checks for exact heading "Independent Auditor\'s Report"',
        category: 'Basic Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: true // Default selected
      },
      {
        id: 'company_format',
        name: 'Address to Members Format',
        description: 'Verifies "To the Members of [Company Name]" format',
        category: 'Basic Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: true
      },
      {
        id: 'profit_loss_consistency',
        name: 'Profit/Loss Opinion Consistency',
        description: 'Matches profit/loss in opinion with your selection',
        category: 'Financial Results Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true
      },
      {
        id: 'signature_date',
        name: 'PKF Signature Block & Date',
        description: 'Verifies complete signature block with correct date',
        category: 'Basic Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: true
      },
      {
        id: 'key_audit_matter',
        name: 'Key Audit Matters Disclosure',
        description: 'Ensures at least one Key Audit Matter is present',
        category: 'Audit Standards',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: false // Optional by default
      },
      {
        id: 'audit_trail_software',
        name: 'Audit Trail Software Disclosure',
        description: 'Checks for accounting software audit trail mention',
        category: 'Technology Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'section_197_reference',
        name: 'Section 197(16) Reference',
        description: 'Verifies Section 197(16) director remuneration disclosure',
        category: 'Regulatory Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'company_name_consistency',
        name: 'Company Name Consistency',
        description: 'Checks for consistent company naming throughout',
        category: 'Basic Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '30',
        isAvailable: true,
        isSelected: true
      }
    );
    
    // Conditional audit report checks
    if (parameters.audit_report_type !== 'Normal') {
      availableChecks.push({
        id: 'financial_statements_type',
        name: `${parameters.audit_report_type} Financial Statements Consistency`,
        description: `Verifies consistent use of "${parameters.audit_report_type}" throughout`,
        category: 'Statement Type Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true
      });
    }
    
    if (parameters.company_listing_status === 'Listed') {
      availableChecks.push({
        id: 'brsr_brr',
        name: parameters.top_1000_or_500 === 'Yes' ? 'BRSR Disclosure Check' : 'BRR Disclosure Check',
        description: parameters.top_1000_or_500 === 'Yes' 
          ? 'Verifies Business Responsibility and Sustainability Report disclosure'
          : 'Verifies Business Responsibility Report disclosure',
        category: 'Sustainability Reporting',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true
      });
    }

    if (parameters.audit_report_type === 'Consolidated') {
      availableChecks.push({
        id: 'consolidated_wording',
        name: 'Consolidated Statements Wording Consistency',
        description: 'Verifies consistent use of "consolidated" throughout documents',
        category: 'Consolidated Compliance',
        documentTypes: ['audit_report'],
        estimatedTime: '45',
        isAvailable: true,
        isSelected: true
      });
    }
  }
  
  // CARO ANNEXURE CHECKS
  if (documents.annexure_a) {
    if (parameters.audit_report_type === 'Consolidated') {
      availableChecks.push({
        id: 'clause_21',
        name: 'CARO Clause (xxi) - Consolidated Only',
        description: 'Verifies only clause (xxi) is present for consolidated reports',
        category: 'CARO Consolidated',
        documentTypes: ['annexure_a'],
        estimatedTime: '60',
        isAvailable: true,
        isSelected: true
      });
    } else if (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal') {
      availableChecks.push(
        {
          id: 'clause_20',
          name: 'CARO Clauses (i-xx) - Standalone Complete',
          description: 'Verifies all 20 mandatory clauses (i) to (xx) are present',
          category: 'CARO Standalone',
          documentTypes: ['annexure_a'],
          estimatedTime: '90',
          isAvailable: true,
          isSelected: true
        },
        {
          id: 'benami_property_clause',
          name: 'CARO Clause (i)(e) - Benami Property',
          description: 'Verifies Benami Property proceedings disclosure',
          category: 'CARO Regulatory Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: true
        },
        {
          id: 'caro_clause_xiii_related_party',
          name: 'CARO Clause (xiii) - Related Party Transactions',
          description: 'Verifies Section 177 & 188 compliance disclosure',
          category: 'Related Party Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: false
        },
        {
          id: 'caro_clause_vii_a_statutory_dues',
          name: 'CARO Clause (vii)(a) - Regular Statutory Dues',
          description: 'Verifies regular deposit of undisputed statutory dues',
          category: 'Statutory Dues Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: false
        },
        {
          id: 'caro_clause_vii_b_disputed_dues',
          name: 'CARO Clause (vii)(b) - Disputed Statutory Dues',
          description: 'Verifies disputed statutory dues disclosure',
          category: 'Statutory Dues Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: false
        }
      );
      
      // Conditional CARO checks
      if (parameters.has_cost_auditor === 'Yes') {
        availableChecks.push({
          id: 'caro_clause_vi_cost_auditor',
          name: 'CARO Clause (vi) - Cost Records Maintenance',
          description: 'Verifies cost records maintenance under Section 148',
          category: 'Cost Audit Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: true
        });
      }
      
      if (parameters.has_internal_auditor === 'Yes') {
        availableChecks.push({
          id: 'internal_auditor_clause_xiv',
          name: 'CARO Clause (xiv) - Internal Auditor',
          description: 'Verifies internal auditor appointment disclosure',
          category: 'Internal Auditor Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: true
        });
      }

      if (parameters.is_nbfc === 'Yes') {
        availableChecks.push({
          id: 'caro_nbfc_exemptions',
          name: 'NBFC CARO Clause Exemptions',
          description: 'Verifies proper NBFC clause exemptions in CARO',
          category: 'NBFC Compliance',
          documentTypes: ['annexure_a'],
          estimatedTime: '45',
          isAvailable: true,
          isSelected: true
        });
      }
    }
  }
  
  // MULTI-DOCUMENT CHECKS
  if (documents.audit_report && documents.annexure_a) {
    availableChecks.push({
      id: 'ar_caro_reference_matching',
      name: 'Audit Report + CARO Reference Matching',
      description: 'Cross-verifies paragraph number references between documents',
      category: 'Cross-Reference Compliance',
      documentTypes: ['audit_report', 'annexure_a'],
      estimatedTime: '60',
      isAvailable: true,
      isSelected: false
    });
  }
  
  if (documents.audit_report && documents.annexure_b) {
    availableChecks.push({
      id: 'ar_ifc_reference_matching',
      name: 'Audit Report + IFC Reference Matching',
      description: 'Cross-verifies IFC annexure references',
      category: 'Cross-Reference Compliance',
      documentTypes: ['audit_report', 'annexure_b'],
      estimatedTime: '60',
      isAvailable: true,
      isSelected: false
    });
  }
  
  if (documents.balance_sheet && documents.annexure_a) {
    availableChecks.push({
      id: 'bs_caro_ppe_check',
      name: 'Balance Sheet + CARO PPE Verification',
      description: 'If PPE > 0, verifies CARO subclauses (i)(a)(A) & (i)(b)',
      category: 'Asset Verification',
      documentTypes: ['balance_sheet', 'annexure_a'],
      estimatedTime: '75',
      isAvailable: true,
      isSelected: false
    });
  }

  if (documents.annual_report && documents.annexure_a && 
      (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal')) {
    availableChecks.push(
      {
        id: 'annual_report_caro_income_tax',
        name: 'Annual Report + CARO Income Tax Cross-Check',
        description: 'Cross-verifies income tax raids disclosure between documents',
        category: 'Annual Report Cross-Compliance',
        documentTypes: ['annual_report', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      },
      {
        id: 'annual_report_caro_fraud',
        name: 'Annual Report + CARO Fraud Cross-Check',
        description: 'Cross-verifies fraud disclosure between documents',
        category: 'Annual Report Cross-Compliance',
        documentTypes: ['annual_report', 'annexure_a'],
        estimatedTime: '90',
        isAvailable: true,
        isSelected: false
      }
    );
  }
  
  return availableChecks;
}

/**
 * Group checks by category for UI display
 */
export function groupChecksByCategory(checks: CheckOption[]): CheckCategory[] {
  const categories: Record<string, CheckOption[]> = {};
  
  checks.forEach(check => {
    if (!categories[check.category]) {
      categories[check.category] = [];
    }
    categories[check.category].push(check);
  });
  
  return Object.entries(categories).map(([name, checks]) => ({
    name,
    checks,
    totalSelected: checks.filter(c => c.isSelected).length,
    totalAvailable: checks.length
  }));
}

/**
 * Get summary statistics for analysis results
 */
export function getAnalysisSummary(results: Record<string, CheckResult>): {
  total: number;
  compliant: number;
  nonCompliant: number;
  compliancePercentage: number;
  byCategory: Record<string, { compliant: number; total: number; percentage: number }>;
} {
  const total = Object.keys(results).length;
  const compliant = Object.values(results).filter(result => result.isCompliant).length;
  const nonCompliant = total - compliant;
  const compliancePercentage = total > 0 ? Math.round((compliant / total) * 100) : 0;
  
  // Group by category (from source field)
  const byCategory: Record<string, { compliant: number; total: number; percentage: number }> = {};
  
  for (const result of Object.values(results)) {
    const category = result.source || 'Basic Compliance';
    
    if (!byCategory[category]) {
      byCategory[category] = { compliant: 0, total: 0, percentage: 0 };
    }
    
    byCategory[category].total++;
    if (result.isCompliant) {
      byCategory[category].compliant++;
    }
  }
  
  // Calculate percentages
  for (const category of Object.keys(byCategory)) {
    const cat = byCategory[category];
    cat.percentage = cat.total > 0 ? Math.round((cat.compliant / cat.total) * 100) : 0;
  }
  
  return {
    total,
    compliant,
    nonCompliant,
    compliancePercentage,
    byCategory
  };
}

/**
 * Helper function to process selected multi-document checks
 */
async function processSelectedMultiDocumentChecks(
  documents: DocumentFiles,
  parameters: AnalysisParameters,
  selectedCheckIds: string[],
  onProgress?: (current: number, total: number, checkName: string) => void
): Promise<Record<string, CheckResult>> {
  
  // Get all available multi-doc checks
  const allMultiDocChecks = getAvailableMultiDocumentChecks(documents, parameters);
  
  // Filter to only selected checks
  const selectedChecks = allMultiDocChecks.filter(check => 
    selectedCheckIds.includes(check.id)
  );
  
  console.log(`Processing ${selectedChecks.length} selected multi-document checks...`);
  
  if (selectedChecks.length === 0) {
    console.log('No multi-document checks selected, skipping...');
    return {};
  }
  
  // Use the existing processAllMultiDocumentChecks but filter results
  const allResults = await processAllMultiDocumentChecks(
    documents,
    parameters,
    onProgress
  );
  
  // Filter results to only selected checks
  const filteredResults: Record<string, CheckResult> = {};
  selectedCheckIds.forEach(checkId => {
    if (allResults[checkId]) {
      filteredResults[checkId] = allResults[checkId];
    }
  });
  
  return filteredResults;
}

/**
 * Process documents for analysis - Complete version with selective check support
 */
export async function processDocumentsForAnalysis(
  documents: DocumentFiles,
  parameters: AnalysisParameters,
  selectedCheckIds?: string[], // NEW: Optional parameter for selective processing
  onProgress?: (progress: ProcessingProgress) => void
): Promise<Record<string, CheckResult>> {
  try {
    console.log('=== STARTING ANALYSIS WITH SELECTIVE CHECK SUPPORT ===');
    console.log('Selected check IDs:', selectedCheckIds);
    console.log('Selective mode:', selectedCheckIds ? 'ENABLED' : 'DISABLED (all checks)');
    
    // Log the AI model routing configuration
    logRoutingConfiguration();
    
    console.log('Available documents:', Object.keys(documents).filter(key => documents[key as keyof DocumentFiles]));
    console.log('Analysis parameters:', {
      company_name: parameters.company_name,
      audit_report_type: parameters.audit_report_type,
      company_listing_status: parameters.company_listing_status,
      top_1000_or_500: parameters.top_1000_or_500,
      is_nbfc: parameters.is_nbfc,
      has_internal_auditor: parameters.has_internal_auditor,
      has_cost_auditor: parameters.has_cost_auditor
    });
    
    // ===========================================
    // DEBUG: Check Annual Report + CARO conditions
    // ===========================================
    const canRunAnnualReportChecks = documents.annual_report && documents.annexure_a && 
      (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal');
    
    console.log('DEBUG: Can run Annual Report + CARO checks:', canRunAnnualReportChecks);
    if (canRunAnnualReportChecks) {
      console.log('✅ Annual Report + CARO cross-compliance checks will be processed');
    }
    
    // ===========================================
    // PHASE 1: BUILD SINGLE DOCUMENT CHECKS LIST
    // ===========================================
    
    console.log('\n🚀 PHASE 1: BUILDING SINGLE DOCUMENT CHECKS LIST');
    
    // Define available single-document checks based on uploaded documents and parameters
    const singleDocumentChecks: Array<{
      id: string, 
      name: string, 
      promptKey: string, 
      docType: keyof DocumentFiles,
      category: string
    }> = [];
    
    // ===========================================
    // AUDIT REPORT CHECKS
    // ===========================================
    if (documents.audit_report) {
      console.log('Adding audit report checks...');
      
      // Always available audit report checks
      singleDocumentChecks.push(
        { 
          id: 'audit_title', 
          name: 'Independent Auditor\'s Report Title', 
          promptKey: 'audit_title', 
          docType: 'audit_report',
          category: 'Basic Compliance'
        },
        { 
          id: 'company_format', 
          name: 'Address to Members Format', 
          promptKey: 'company_format', 
          docType: 'audit_report',
          category: 'Basic Compliance'
        },
        { 
          id: 'signature_date', 
          name: 'PKF Signature Block & Date', 
          promptKey: 'signature_date', 
          docType: 'audit_report',
          category: 'Basic Compliance'
        },
        { 
          id: 'key_audit_matter', 
          name: 'Key Audit Matters Disclosure', 
          promptKey: 'key_audit_matter', 
          docType: 'audit_report',
          category: 'Audit Standards'
        },
        { 
          id: 'audit_trail_software', 
          name: 'Audit Trail Software Disclosure', 
          promptKey: 'audit_trail_software', 
          docType: 'audit_report',
          category: 'Technology Compliance'
        },
        { 
          id: 'section_197_reference', 
          name: 'Section 197(16) Reference', 
          promptKey: 'section_197_reference', 
          docType: 'audit_report',
          category: 'Regulatory Compliance'
        },
        { 
          id: 'company_name_consistency', 
          name: 'Company Name Consistency', 
          promptKey: 'company_name_consistency', 
          docType: 'audit_report',
          category: 'Basic Compliance'
        },
        {
          id: 'profit_loss_consistency',
          name: 'Profit/Loss Opinion Consistency',
          promptKey: 'profit_loss_opinion',
          docType: 'audit_report',
          category: 'Financial Results Compliance'
        }
      );

      // Conditional audit report checks
      if (parameters.audit_report_type !== 'Normal') {
        console.log('Adding financial statements type check for:', parameters.audit_report_type);
        singleDocumentChecks.push({
          id: 'financial_statements_type',
          name: `${parameters.audit_report_type} Financial Statements Consistency`,
          promptKey: 'financial_statements_type',
          docType: 'audit_report',
          category: 'Statement Type Compliance'
        });
      }
      
      if (parameters.company_listing_status === 'Listed') {
        console.log('Adding BRSR/BRR check for listed company, Top 1000/500:', parameters.top_1000_or_500);
        singleDocumentChecks.push({
          id: 'brsr_brr',
          name: parameters.top_1000_or_500 === 'Yes' ? 'BRSR Disclosure Check' : 'BRR Disclosure Check',
          promptKey: 'brsr_brr',
          docType: 'audit_report',
          category: 'Sustainability Reporting'
        });
      }
      
      if (parameters.audit_report_type === 'Consolidated') {
        console.log('Adding consolidated wording consistency check');
        singleDocumentChecks.push({
          id: 'consolidated_wording',
          name: 'Consolidated Statements Wording Consistency',
          promptKey: 'consolidated_wording',
          docType: 'audit_report',
          category: 'Consolidated Compliance'
        });
      }
    } else {
      console.log('⏭️  Audit report not provided - skipping audit report checks');
    }

    // ===========================================
    // CARO ANNEXURE CHECKS (CRITICAL LOGIC)
    // ===========================================
    if (documents.annexure_a) {
      console.log('=== PROCESSING CARO CHECKS ===');
      console.log('Audit Report Type:', parameters.audit_report_type);
      
      // MUTUALLY EXCLUSIVE LOGIC FOR CARO CLAUSES
      if (parameters.audit_report_type === 'Consolidated') {
        console.log('✅ Adding ONLY clause_21 check for Consolidated report');
        singleDocumentChecks.push({
          id: 'clause_21',
          name: 'CARO Clause (xxi) - Consolidated Only',
          promptKey: 'clause_21',
          docType: 'annexure_a',
          category: 'CARO Consolidated'
        });
      } 
      else if (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal') {
        console.log('✅ Adding Standalone/Normal CARO checks');
        singleDocumentChecks.push(
          {
            id: 'clause_20',
            name: 'CARO Clauses (i-xx) - Standalone Complete',
            promptKey: 'clause_20',
            docType: 'annexure_a',
            category: 'CARO Standalone'
          },
          {
            id: 'benami_property_clause',
            name: 'CARO Clause (i)(e) - Benami Property Disclosure',
            promptKey: 'benami_property_clause',
            docType: 'annexure_a',
            category: 'CARO Regulatory Compliance'
          },
          {
            id: 'caro_clause_xiii_related_party',
            name: 'CARO Clause (xiii) - Related Party Transactions Compliance',
            promptKey: 'caro_clause_xiii_related_party',
            docType: 'annexure_a',
            category: 'Related Party Compliance'
          },
          {
            id: 'caro_clause_vii_a_statutory_dues',
            name: 'CARO Clause (vii)(a) - Regular Deposit of Undisputed Statutory Dues',
            promptKey: 'caro_clause_vii_a_statutory_dues',
            docType: 'annexure_a',
            category: 'Statutory Dues Compliance'
          },
          {
            id: 'caro_clause_vii_b_disputed_dues',
            name: 'CARO Clause (vii)(b) - Disputed Statutory Dues Disclosure',
            promptKey: 'caro_clause_vii_b_disputed_dues',
            docType: 'annexure_a',
            category: 'Statutory Dues Compliance'
          }
        );

        // Conditional CARO checks
        if (parameters.has_cost_auditor === 'Yes') {
          console.log('✅ Adding Cost Auditor clause (vi) check - company HAS Cost Auditor');
          singleDocumentChecks.push({
            id: 'caro_clause_vi_cost_auditor',
            name: 'CARO Clause (vi) - Cost Records Maintenance',
            promptKey: 'caro_clause_vi_cost_auditor',
            docType: 'annexure_a',
            category: 'Cost Audit Compliance'
          });
        }

        if (parameters.has_internal_auditor === 'Yes') {
          console.log('✅ Adding Internal Auditor clause (xiv) check - company HAS internal auditor');
          singleDocumentChecks.push({
            id: 'internal_auditor_clause_xiv',
            name: 'CARO Clause (xiv) - Internal Auditor Appointed Disclosure',
            promptKey: 'internal_auditor_clause_xiv',
            docType: 'annexure_a',
            category: 'Internal Auditor Compliance'
          });
        }

        if (parameters.is_nbfc === 'Yes') {
          console.log('✅ Adding NBFC CARO exemption checks');
          singleDocumentChecks.push({
            id: 'caro_nbfc_exemptions',
            name: 'NBFC CARO Clause Exemptions',
            promptKey: 'caro_nbfc_iii_clause_present',
            docType: 'annexure_a',
            category: 'NBFC Compliance'
          });
        }
      }
      else {
        console.log('⚠️  Unknown audit report type, skipping CARO clause checks');
      }
    }
    
    // ===========================================
    // APPLY SELECTIVE FILTERING
    // ===========================================
    
    // FILTER THE CHECKS BASED ON SELECTION
    const filteredSingleDocChecks = selectedCheckIds 
      ? singleDocumentChecks.filter(check => selectedCheckIds.includes(check.id))
      : singleDocumentChecks; // If no selection, run all (backward compatibility)
    
    console.log(`\n=== SELECTIVE FILTERING RESULTS ===`);
    console.log(`Total available single-doc checks: ${singleDocumentChecks.length}`);
    console.log(`Filtered single-doc checks to run: ${filteredSingleDocChecks.length}`);
    if (selectedCheckIds) {
      console.log(`Selected check IDs:`, selectedCheckIds);
      console.log(`Filtered check IDs:`, filteredSingleDocChecks.map(c => c.id));
    }
    
    // Get available multi-document checks
    const availableMultiDocChecks = getAvailableMultiDocumentChecks(documents, parameters);
    
    // FILTER MULTI-DOC CHECKS TOO
    const filteredMultiDocChecks = selectedCheckIds 
      ? availableMultiDocChecks.filter(check => selectedCheckIds.includes(check.id))
      : availableMultiDocChecks;
    
    console.log(`Total available multi-doc checks: ${availableMultiDocChecks.length}`);
    console.log(`Filtered multi-doc checks to run: ${filteredMultiDocChecks.length}`);
    
    // Calculate Annual Report + CARO checks count
    const annualReportCaroCheckIds = [
      'annual_report_caro_income_tax',
      'annual_report_caro_defaults', 
      'annual_report_caro_rights_issue',
      'annual_report_caro_fraud',
      'annual_report_caro_whistleblower',
      'annual_report_caro_cost_records'
    ];
    
    const filteredAnnualReportChecks = selectedCheckIds 
      ? annualReportCaroCheckIds.filter(checkId => selectedCheckIds.includes(checkId))
      : (canRunAnnualReportChecks ? annualReportCaroCheckIds : []);
    
    console.log(`Annual Report + CARO checks to run: ${filteredAnnualReportChecks.length}`);
    
    // Calculate totals for progress
    const totalSingleChecks = filteredSingleDocChecks.length;
    const totalMultiChecks = filteredMultiDocChecks.length;
    const totalAnnualReportChecks = filteredAnnualReportChecks.length;
    const totalAllChecks = totalSingleChecks + totalMultiChecks + totalAnnualReportChecks;
    
    console.log(`\n=== FINAL CHECK COUNTS FOR PROCESSING ===`);
    console.log(`Single-doc checks to run: ${totalSingleChecks}`);
    console.log(`Multi-doc checks to run: ${totalMultiChecks}`);
    console.log(`Annual Report + CARO checks to run: ${totalAnnualReportChecks}`);
    console.log(`Total checks to run: ${totalAllChecks}`);
    
    if (totalAllChecks === 0) {
      throw new Error('No checks selected to run');
    }
    
    // Log the checks that will actually run
    console.log(`\n=== CHECKS TO BE PROCESSED ===`);
    filteredSingleDocChecks.forEach((check, index) => {
      console.log(`${index + 1}. ${check.name} (${check.id}) - ${check.category}`);
    });
    
    if (filteredMultiDocChecks.length > 0) {
      console.log(`\nMulti-Document Checks:`);
      filteredMultiDocChecks.forEach((check, index) => {
        console.log(`${index + 1}. ${check.name} (${check.id}) - ${check.category}`);
      });
    }
    
    if (filteredAnnualReportChecks.length > 0) {
      console.log(`\nAnnual Report + CARO Checks:`);
      filteredAnnualReportChecks.forEach((checkId, index) => {
        console.log(`${index + 1}. ${checkId}`);
      });
    }
    
    // Initialize progress with correct total
    if (onProgress) {
      onProgress({
        completed: 0,
        total: totalAllChecks, // Use filtered total
        currentCheck: 'Initializing selective analysis...',
        phase: 'single-document'
      });
    }
    
    const singleDocResults: Record<string, CheckResult> = {};
    let completed = 0;
    
    // ===========================================
    // PROCESS FILTERED SINGLE DOCUMENT CHECKS
    // ===========================================
    console.log('\n🚀 PHASE 1: PROCESSING SELECTED SINGLE DOCUMENT CHECKS');
    
    for (const check of filteredSingleDocChecks) { // Use filtered list
      try {
        console.log(`\n--- Processing Selected Single-Doc Check ${completed + 1}/${totalSingleChecks} ---`);
        console.log(`Check: ${check.name}`);
        console.log(`ID: ${check.id}`);
        console.log(`Category: ${check.category}`);
        console.log(`Document: ${check.docType}`);
        
        // Update progress
        if (onProgress) {
          onProgress({
            completed,
            total: totalAllChecks,
            currentCheck: check.name,
            phase: 'single-document'
          });
        }
        
        // Get the document file
        const documentFile = documents[check.docType];
        if (!documentFile) {
          console.error(`❌ Document ${check.docType} not available for check ${check.id}`);
          singleDocResults[check.id] = {
            isCompliant: false,
            explanation: `Required document ${check.docType} not available for analysis`,
            confidence: 0,
            detail: `Please upload the ${check.docType} document to run this check`,
            source: check.category
          };
          completed++;
          continue;
        }
        
        // Process the check with AI model routing
        console.log(`🔄 Processing selected check with AI model routing...`);
        const result = await processDocumentCheckWithRouting(
          documentFile,
          check.id,
          check.promptKey,
          {
            company_name: parameters.company_name,
            audit_date: parameters.audit_date.toISOString(),
            profit_or_loss: parameters.profit_or_loss,
            company_listing_status: parameters.company_listing_status,
            top_1000_or_500: parameters.top_1000_or_500,
            audit_report_type: parameters.audit_report_type,
            audit_opinion_type: parameters.audit_opinion_type,
            is_nbfc: parameters.is_nbfc,
            has_cost_auditor: parameters.has_cost_auditor,
            has_internal_auditor: parameters.has_internal_auditor,
            report_type: parameters.audit_report_type,
            related_party_note_number: parameters.related_party_note_number
          }
        );
        
        singleDocResults[check.id] = {
          ...result,
          source: check.category
        };
        completed++;
        
        console.log(`✅ Completed selected check: ${check.name} - ${result.isCompliant ? 'COMPLIANT' : 'NON-COMPLIANT'}`);
        if (!result.isCompliant) {
          console.log(`   Reason: ${result.explanation?.substring(0, 100)}...`);
        }
        
        // Small delay to prevent API rate limiting
        await new Promise(resolve => setTimeout(resolve, 200));
        
      } catch (error) {
        console.error(`❌ Error processing selected check ${check.id}:`, error);
        singleDocResults[check.id] = {
          isCompliant: false,
          explanation: `Technical error during check processing: ${error instanceof Error ? error.message : String(error)}`,
          confidence: 0.3,
          detail: 'This check could not be completed due to a processing error. Please try again.',
          source: check.category
        };
        completed++;
      }
    }
    
    console.log(`\n✅ PHASE 1 COMPLETE: ${Object.keys(singleDocResults).length} selected single-document checks processed`);
    
    // ===========================================
    // PHASE 2: PROCESS SELECTED MULTI-DOCUMENT CHECKS
    // ===========================================
    
    console.log('\n🚀 PHASE 2: PROCESSING SELECTED MULTI-DOCUMENT CHECKS');
    
    if (onProgress) {
      onProgress({
        completed: totalSingleChecks,
        total: totalAllChecks,
        currentCheck: 'Starting selected multi-document analysis...',
        phase: 'multi-document'
      });
    }
    
    let multiDocResults: Record<string, CheckResult> = {};
    let currentMultiDocProgress = 0;
    
    // ===========================================
    // REGULAR MULTI-DOCUMENT CHECKS
    // ===========================================
    if (filteredMultiDocChecks.length > 0) {
      console.log(`\n📋 Processing ${filteredMultiDocChecks.length} selected multi-document checks...`);
      
      multiDocResults = await processSelectedMultiDocumentChecks(
        documents,
        parameters,
        filteredMultiDocChecks.map(c => c.id), // Pass selected IDs
        (current, total, checkName) => {
          currentMultiDocProgress = current;
          if (onProgress) {
            onProgress({
              completed: totalSingleChecks + current,
              total: totalAllChecks,
              currentCheck: checkName,
              phase: 'multi-document'
            });
          }
        }
      );
      
      console.log(`✅ Selected multi-document checks complete: ${Object.keys(multiDocResults).length} checks processed`);
    } else {
      console.log(`⏭️  No multi-document checks selected`);
    }
    
    // ===========================================
    // ANNUAL REPORT + CARO CROSS-COMPLIANCE CHECKS
    // ===========================================
    if (filteredAnnualReportChecks.length > 0 && canRunAnnualReportChecks) {
      console.log('\n📋 PROCESSING SELECTED ANNUAL REPORT + CARO CROSS-COMPLIANCE CHECKS');
      console.log('Using Ctrl+F text search for Annual Report + Gemini AI for CARO clause analysis');
      
      try {
        // Import the Annual Report + CARO processor
        const { processAllAnnualReportCaroChecks } = await import('./annualReportCaroProcessor');
        
        const annualReportCaroResults = await processAllAnnualReportCaroChecks(
          documents.annual_report!,
          documents.annexure_a!,
          {
            company_name: parameters.company_name,
            audit_date: parameters.audit_date.toISOString(),
            profit_or_loss: parameters.profit_or_loss,
            company_listing_status: parameters.company_listing_status,
            top_1000_or_500: parameters.top_1000_or_500,
            audit_report_type: parameters.audit_report_type,
            audit_opinion_type: parameters.audit_opinion_type,
            is_nbfc: parameters.is_nbfc,
            report_type: parameters.audit_report_type
          },
          (current, total, checkName) => {
            if (onProgress) {
              onProgress({
                completed: totalSingleChecks + totalMultiChecks + current,
                total: totalAllChecks,
                currentCheck: checkName,
                phase: 'multi-document'
              });
            }
          }
        );
        
        // Filter Annual Report results to only selected checks
        const filteredAnnualReportResults: Record<string, CheckResult> = {};
        filteredAnnualReportChecks.forEach(checkId => {
          if (annualReportCaroResults[checkId]) {
            filteredAnnualReportResults[checkId] = annualReportCaroResults[checkId];
          }
        });
        
        // Merge filtered Annual Report + CARO results with multi-document results
        Object.assign(multiDocResults, filteredAnnualReportResults);
        
        console.log(`✅ Selected Annual Report + CARO cross-compliance checks complete: ${Object.keys(filteredAnnualReportResults).length} checks processed`);
        
      } catch (error) {
        console.error('❌ Error processing selected Annual Report + CARO checks:', error);
        
        // Add error results for selected Annual Report checks
        filteredAnnualReportChecks.forEach(checkId => {
          multiDocResults[checkId] = {
            isCompliant: false,
            explanation: `Error processing Annual Report + CARO cross-compliance check: ${error instanceof Error ? error.message : String(error)}`,
            confidence: 0.3,
            source: 'Annual Report Cross-Compliance'
          };
        });
      }
    } else if (filteredAnnualReportChecks.length > 0) {
      console.log('⏭️  Annual Report + CARO checks selected but skipped - missing documents or wrong report type');
      if (!documents.annual_report) console.log('   Missing: Annual Report');
      if (!documents.annexure_a) console.log('   Missing: CARO Annexure A');
      if (parameters.audit_report_type === 'Consolidated') console.log('   Reason: Consolidated reports not supported for this check');
    } else {
      console.log('⏭️  No Annual Report + CARO checks selected');
    }
    
    console.log(`\n✅ PHASE 2 COMPLETE: ${Object.keys(multiDocResults).length} selected multi-document and cross-compliance checks processed`);
    
    // ===========================================
    // FINAL PROCESSING
    // ===========================================
    
    // Combine all results
    const allResults = { ...singleDocResults, ...multiDocResults };
    
    // Final progress update
    if (onProgress) {
      onProgress({
        completed: totalAllChecks,
        total: totalAllChecks,
        currentCheck: 'Selective analysis complete',
        phase: 'complete'
      });
    }
    
    const summary = getAnalysisSummary(allResults);
    
    console.log('\n🎉 SELECTIVE ANALYSIS COMPLETE ===');
    console.log(`📊 Selected Checks Processed: ${Object.keys(allResults).length}`);
    console.log(`   - Single Document: ${Object.keys(singleDocResults).length}`);
    console.log(`   - Multi Document: ${Object.keys(multiDocResults).length - totalAnnualReportChecks}`);
    console.log(`   - Annual Report + CARO: ${totalAnnualReportChecks}`);
    console.log(`📈 Compliance Summary:`, summary);
    console.log(`📋 Results by Check:`);
    
    // Log results by category
    const resultsByCategory: Record<string, Array<{id: string, result: CheckResult}>> = {};
    Object.entries(allResults).forEach(([checkId, result]) => {
      const category = result.source || 'Unknown';
      if (!resultsByCategory[category]) {
        resultsByCategory[category] = [];
      }
      resultsByCategory[category].push({ id: checkId, result });
    });
    
    Object.entries(resultsByCategory).forEach(([category, checks]) => {
      console.log(`\n📂 ${category}:`);
      checks.forEach(({ id, result }) => {
        console.log(`  ${id}: ${result.isCompliant ? '✅ PASS' : '❌ FAIL'}`);
      });
    });
    
    return allResults;
    
  } catch (error) {
    console.error('💥 CRITICAL ERROR in selective document analysis:', error);
    throw error;
  }
}

/**
 * Get recommended documents based on parameters
 */
export function getRecommendedDocuments(parameters: AnalysisParameters): {
  required: string[];
  recommended: string[];
  optional: string[];
  multiDocumentBenefits: string[];
} {
  const required: string[] = []; // No documents are mandatory
  const recommended = ['audit_report']; // Audit report is now recommended, not required
  const optional: string[] = [];
  const multiDocumentBenefits: string[] = [];
  
  // CARO is highly recommended for most comprehensive checks
  recommended.push('annexure_a');
  
  // IFC is recommended for internal control validation
  recommended.push('annexure_b');
  
  // Multi-document benefits
  if (parameters.company_listing_status === 'Listed') {
    recommended.push('secretarial_compliance');
    multiDocumentBenefits.push('Secretarial + CARO cross-validation for Section 192 compliance');
  }
  
  // Balance sheet for financial cross-validation
  optional.push('balance_sheet');
  multiDocumentBenefits.push('Balance Sheet + CARO investment compliance verification');
  
  // Notes for detailed analysis and cross-referencing
  optional.push('notes');
  optional.push('pl_notes');
  multiDocumentBenefits.push('Balance Sheet + Notes + CARO comprehensive investment analysis');
  
  // Annual report for holistic compliance checking
  optional.push('annual_report');
  
  // Additional multi-document benefits
  if (parameters.is_nbfc === 'Yes') {
    multiDocumentBenefits.push('NBFC-specific compliance across Audit Report + CARO + Balance Sheet');
  }
  
  multiDocumentBenefits.push('Cross-document consistency verification for company information');
  
  return { required, recommended, optional, multiDocumentBenefits };
}

/**
 * Get analysis preview - what checks will be performed
 */
export function getAnalysisPreview(
  documents: DocumentFiles,
  parameters: AnalysisParameters,
  selectedCheckIds?: string[]
): {
  singleDocumentChecks: Array<{id: string, name: string, category: string}>;
  multiDocumentChecks: Array<{id: string, name: string, category: string}>;
  annualReportCaroChecks: Array<{id: string, name: string, category: string}>;
  totalChecks: number;
  estimatedDuration: string;
} {
  const singleDocumentChecks: Array<{id: string, name: string, category: string}> = [];
  
  // Simulate the same logic as the main processor to get single-doc checks
  if (documents.audit_report) {
    const auditChecks = [
      { id: 'audit_title', name: 'Independent Auditor\'s Report Title', category: 'Basic Compliance' },
      { id: 'company_format', name: 'Address to Members Format', category: 'Basic Compliance' },
      { id: 'signature_date', name: 'PKF Signature Block & Date', category: 'Basic Compliance' },
      { id: 'key_audit_matter', name: 'Key Audit Matters Disclosure', category: 'Audit Standards' },
      { id: 'audit_trail_software', name: 'Audit Trail Software Disclosure', category: 'Technology Compliance' },
      { id: 'section_197_reference', name: 'Section 197(16) Reference', category: 'Regulatory Compliance' },
      { id: 'company_name_consistency', name: 'Company Name Consistency', category: 'Basic Compliance' },
      { id: 'profit_loss_consistency', name: 'Profit/Loss Opinion Consistency', category: 'Financial Results Compliance'}
    ];
    
    // Filter audit checks if selective mode
    const filteredAuditChecks = selectedCheckIds 
      ? auditChecks.filter(check => selectedCheckIds.includes(check.id))
      : auditChecks;
    
    singleDocumentChecks.push(...filteredAuditChecks);
    
    if (parameters.audit_report_type !== 'Normal') {
      const typeCheck = {
        id: 'financial_statements_type',
        name: `${parameters.audit_report_type} Financial Statements Consistency`,
        category: 'Statement Type Compliance'
      };
      if (!selectedCheckIds || selectedCheckIds.includes(typeCheck.id)) {
        singleDocumentChecks.push(typeCheck);
      }
    }
    
    if (parameters.company_listing_status === 'Listed') {
      const brsrCheck = {
        id: 'brsr_brr',
        name: parameters.top_1000_or_500 === 'Yes' ? 'BRSR Disclosure Check' : 'BRR Disclosure Check',
        category: 'Sustainability Reporting'
      };
      if (!selectedCheckIds || selectedCheckIds.includes(brsrCheck.id)) {
        singleDocumentChecks.push(brsrCheck);
      }
    }
    
    if (parameters.audit_report_type === 'Consolidated') {
      const consolidatedCheck = {
        id: 'consolidated_wording',
        name: 'Consolidated Statements Wording Consistency',
        category: 'Consolidated Compliance'
      };
      if (!selectedCheckIds || selectedCheckIds.includes(consolidatedCheck.id)) {
        singleDocumentChecks.push(consolidatedCheck);
      }
    }
  }
  
  if (documents.annexure_a) {
    const caroChecks = [];
    
    if (parameters.audit_report_type === 'Consolidated') {
      caroChecks.push({
        id: 'clause_21',
        name: 'CARO Clause (xxi) - Consolidated Only',
        category: 'CARO Consolidated'
      });
    } else if (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal') {
      caroChecks.push(
        {
          id: 'clause_20',
          name: 'CARO Clauses (i-xx) - Standalone Complete',
          category: 'CARO Standalone'
        },
        {
          id: 'benami_property_clause',
          name: 'CARO Clause (i)(e) - Benami Property Disclosure',
          category: 'CARO Regulatory Compliance'
        },
        {
          id: 'caro_clause_xiii_related_party',
          name: 'CARO Clause (xiii) - Related Party Transactions Compliance',
          category: 'Related Party Compliance'
        },
        {
          id: 'caro_clause_vii_a_statutory_dues',
          name: 'CARO Clause (vii)(a) - Regular Deposit of Undisputed Statutory Dues',
          category: 'Statutory Dues Compliance'
        },
        {
          id: 'caro_clause_vii_b_disputed_dues',
          name: 'CARO Clause (vii)(b) - Disputed Statutory Dues Disclosure',
          category: 'Statutory Dues Compliance'
        }
      );
      
      // Add Cost Auditor check (only if has_cost_auditor = Yes)
      if (parameters.has_cost_auditor === 'Yes') {
        caroChecks.push({
          id: 'caro_clause_vi_cost_auditor',
          name: 'CARO Clause (vi) - Cost Records Maintenance',
          category: 'Cost Audit Compliance'
        });
      }
      
      // Add Internal Auditor check (only if has_internal_auditor = Yes)
      if (parameters.has_internal_auditor === 'Yes') {
        caroChecks.push({
          id: 'internal_auditor_clause_xiv',
          name: 'CARO Clause (xiv) - Internal Auditor Appointed Disclosure',
          category: 'Internal Auditor Compliance'
        });
      }
      
      // Add NBFC check (only if is_nbfc = Yes)
      if (parameters.is_nbfc === 'Yes') {
        caroChecks.push({
          id: 'caro_nbfc_exemptions',
          name: 'NBFC CARO Clause Exemptions',
          category: 'NBFC Compliance'
        });
      }
    }
    
    // Filter CARO checks if selective mode
    const filteredCaroChecks = selectedCheckIds 
      ? caroChecks.filter(check => selectedCheckIds.includes(check.id))
      : caroChecks;
    
    singleDocumentChecks.push(...filteredCaroChecks);
  }

  // Get multi-document checks
  const allMultiDocChecks = getAvailableMultiDocumentChecks(documents, parameters);
  const filteredMultiDocChecks = selectedCheckIds 
    ? allMultiDocChecks.filter(check => selectedCheckIds.includes(check.id))
    : allMultiDocChecks;
    
  const multiDocumentChecks = filteredMultiDocChecks.map(check => ({
    id: check.id,
    name: check.name,
    category: check.category
  }));
  
  // Get Annual Report + CARO checks
  const annualReportCaroChecks: Array<{id: string, name: string, category: string}> = [];
  
  if (documents.annual_report && documents.annexure_a && 
      (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal')) {
    
    const allAnnualReportChecks = [
      { id: 'annual_report_caro_income_tax', name: 'Income Tax Raids Cross-Compliance', category: 'Annual Report Cross-Compliance' },
      { id: 'annual_report_caro_defaults', name: 'Defaults/Wilful Defaulter Cross-Compliance', category: 'Annual Report Cross-Compliance' },
      { id: 'annual_report_caro_rights_issue', name: 'Rights Issue Cross-Compliance', category: 'Annual Report Cross-Compliance' },
      { id: 'annual_report_caro_fraud', name: 'Fraud Cross-Compliance', category: 'Annual Report Cross-Compliance' },
      { id: 'annual_report_caro_whistleblower', name: 'Whistle-blower Cross-Compliance', category: 'Annual Report Cross-Compliance' },
      { id: 'annual_report_caro_cost_records', name: 'Cost Records Cross-Compliance', category: 'Annual Report Cross-Compliance' }
    ];
    
    // Filter Annual Report checks if selective mode
    const filteredAnnualReportChecks = selectedCheckIds 
      ? allAnnualReportChecks.filter(check => selectedCheckIds.includes(check.id))
      : allAnnualReportChecks;
    
    annualReportCaroChecks.push(...filteredAnnualReportChecks);
  }
  
  const totalChecks = singleDocumentChecks.length + multiDocumentChecks.length + annualReportCaroChecks.length;
  const estimatedMinutes = Math.ceil(totalChecks * 0.5); // Rough estimate: 30 seconds per check
  const estimatedDuration = estimatedMinutes < 60 ? 
    `${estimatedMinutes} minutes` : 
    `${Math.floor(estimatedMinutes / 60)}h ${estimatedMinutes % 60}m`;
  
  return {
    singleDocumentChecks,
    multiDocumentChecks,
    annualReportCaroChecks,
    totalChecks,
    estimatedDuration
  };
}

/**
 * Debug function to log check conditions
 */
export function debugCheckConditions(parameters: AnalysisParameters, documents: DocumentFiles): void {
  console.log('\n=== DEBUG: CHECK CONDITIONS WITH SELECTIVE SUPPORT ===');
  console.log('Parameters:', parameters);
  console.log('Available documents:', Object.keys(documents).filter(key => documents[key as keyof DocumentFiles]));
  
  console.log('\nSingle-Document CARO Logic Evaluation:');
  console.log(`- Audit Report Type: "${parameters.audit_report_type}"`);
  console.log(`- Has Internal Auditor = "Yes": ${parameters.has_internal_auditor === 'Yes'}`);
  console.log(`- Has Cost Auditor = "Yes": ${parameters.has_cost_auditor === 'Yes'}`);
  console.log(`- Has Annexure A: ${!!documents.annexure_a}`);
  console.log(`- Is NBFC: ${parameters.is_nbfc}`);
  
  if (documents.annexure_a) {
    if (parameters.audit_report_type === 'Consolidated') {
      console.log('✅ Will run: clause_21 (only xxi clause for consolidated)');
    } else if (parameters.audit_report_type === 'Standalone' || parameters.audit_report_type === 'Normal') {
      console.log('✅ Will run: clause_20 (clauses i-xx for standalone/normal)');
    } else {
      console.log('⚠️  Unknown audit type - no CARO clause checks will run');
    }
  }
  
  console.log('\nMulti-Document Logic Evaluation:');
  const multiDocChecks = getAvailableMultiDocumentChecks(documents, parameters);
  console.log(`- Available multi-document checks: ${multiDocChecks.length}`);
  multiDocChecks.forEach(check => {
    console.log(`  ✅ ${check.name} (requires: ${check.documents.join(', ')})`);
  });
  
  console.log('\nListing Logic Evaluation:');
  console.log(`- Listing Status: ${parameters.company_listing_status}`);
  console.log(`- Top 1000/500: ${parameters.top_1000_or_500}`);
  if (parameters.company_listing_status === 'Listed') {
    console.log(`✅ Will run: ${parameters.top_1000_or_500 === 'Yes' ? 'BRSR' : 'BRR'} check`);
    if (documents.secretarial_compliance && documents.annexure_a) {
      console.log(`✅ Will run: Secretarial + CARO Section 192 multi-document check`);
    }
  }
  
  console.log('=== END DEBUG ===\n');
}

/**
 * Export helper functions for backward compatibility
 */
export function processIndependentChecks(
  documents: DocumentFiles,
  parameters: AnalysisParameters,
  applicableChecks: any[],
  onProgress?: (progress: ProcessingProgress) => void
): Promise<Record<string, CheckResult>> {
  // This function is now integrated into the main processDocumentsForAnalysis
  // Keeping for backward compatibility
  return processDocumentsForAnalysis(documents, parameters, undefined, onProgress);
}

export function processInterlinkedChecks(
  documents: DocumentFiles,
  parameters: AnalysisParameters,
  applicableChecks: any[],
  independentResults: Record<string, CheckResult>,
  onProgress?: (progress: ProcessingProgress) => void
): Promise<Record<string, CheckResult>> {
  // This function is now integrated into the main processDocumentsForAnalysis
  // Multi-document checks are handled by the enhanced processor
  return Promise.resolve({});
}