// multiDocumentPrompts.ts - Complete prompt definitions for multi-document checks

export interface PromptTemplate {
  key: string;
  template: string;
  variables?: string[];
}

export const MULTI_DOCUMENT_PROMPTS: Record<string, PromptTemplate> = {


  // ===========================================
// COMPREHENSIVE SECRETARIAL AUDIT MULTI-SECTION CHECKS
// ===========================================

// Comprehensive Secretarial Audit Multi-Section Check
secretarial_comprehensive_sections_check: {
  key: 'secretarial_comprehensive_sections_check',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Secretarial Audit Report PDF to check for qualifications, observations, or non-compliance issues related to ALL the following sections of the Companies Act, 2013:

    **SECTIONS TO CHECK:**
    1. **Section 185 & 186** - Loans to directors and investments by company
    2. **Sections 73-76** - Acceptance of deposits from public
    3. **Sections 42 & 62** - Private placement and rights issue of securities
    4. **Sub Section (12) of Section 143** - Reporting of fraud by auditor
    5. **Sections 177 & 188** - Audit Committee and Related Party Transactions
    6. **Section 192** - Restriction on non-cash transactions with directors
    7. **Section 135** - Corporate Social Responsibility

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 7 lines, nothing else:

    Section_185_186_Status: [Has Issues/Clean/Not Mentioned]
    Sections_73_76_Status: [Has Issues/Clean/Not Mentioned]
    Sections_42_62_Status: [Has Issues/Clean/Not Mentioned]
    Section_143_12_Status: [Has Issues/Clean/Not Mentioned]
    Sections_177_188_Status: [Has Issues/Clean/Not Mentioned]
    Section_192_Status: [Has Issues/Clean/Not Mentioned]
    Section_135_Status: [Has Issues/Clean/Not Mentioned]

    **EVALUATION RULES:**
    - If ANY qualification, observation, or non-compliance is found → "Has Issues"
    - If section is mentioned but no issues/qualifications noted → "Clean"
    - If section is not mentioned at all → "Not Mentioned"

    **EXAMPLE RESPONSE:**
    Section_185_186_Status: Clean
    Sections_73_76_Status: Has Issues
    Sections_42_62_Status: Not Mentioned
    Section_143_12_Status: Clean
    Sections_177_188_Status: Has Issues
    Section_192_Status: Clean
    Section_135_Status: Has Issues
  `,
  variables: []
},

caro_comprehensive_clauses_check: {
  key: 'caro_comprehensive_clauses_check',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to check for the presence of ALL the following clauses:

    **CLAUSES TO CHECK:**
    1. **Clause (iv)** - Related to Sections 185 & 186 (loans to directors, investments)
    2. **Clause (v)** - Related to Sections 73-76 (acceptance of deposits)
    3. **Clause (x)(b)** - Related to Sections 42 & 62 (private placement, rights issue)
    4. **Clause (xi)(b)** - Related to Sub Section (12) of Section 143 (fraud reporting)
    5. **Clause (xiii)** - Related to Sections 177 & 188 (audit committee, related party transactions)
    6. **Clause (xv)** - Related to Section 192 (non-cash transactions with directors)
    7. **Clause (xx)** - Related to Section 135 (Corporate Social Responsibility)

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 7 lines, nothing else:

    Clause_IV_Status: [Present/Incomplete/Missing]
    Clause_V_Status: [Present/Incomplete/Missing]
    Clause_X_B_Status: [Present/Incomplete/Missing]
    Clause_XI_B_Status: [Present/Incomplete/Missing]
    Clause_XIII_Status: [Present/Incomplete/Missing]
    Clause_XV_Status: [Present/Incomplete/Missing]
    Clause_XX_Status: [Present/Incomplete/Missing]

    **EVALUATION RULES:**
    - If clause is present and properly addresses relevant matters → "Present"
    - If clause is present but incomplete or inadequate → "Incomplete"
    - If clause is completely missing → "Missing"

    **EXAMPLE RESPONSE:**
    Clause_IV_Status: Present
    Clause_V_Status: Missing
    Clause_X_B_Status: Present
    Clause_XI_B_Status: Present
    Clause_XIII_Status: Incomplete
    Clause_XV_Status: Present
    Clause_XX_Status: Missing
  `,
  variables: []
},
  // ===========================================
  // Balance sheet and caro Annexure A prompts for the Intangible Assets Cost
  // ===========================================

  // Balance Sheet Intangible Assets Cost Check
bs_intangible_assets_cost: {
  key: 'bs_intangible_assets_cost',
  template: `
    Analyze this Balance Sheet PDF to extract the total cost/gross value of intangible assets.

    **SPECIFIC INSTRUCTIONS:**
    1. Look for "Intangible Assets" section in the Balance Sheet
    2. Find the cost/gross value (before depreciation/amortization) of intangible assets
    3. Common line items include:
       - "Intangible Assets"
       - "Computer Software"
       - "Patents and Trademarks"
       - "Goodwill" (if applicable)
       - "Development Costs"
       - "Licenses"

    4. Extract the total cost value (not net book value)
    5. Look in both current year and previous year columns
    6. Amount should be in lakhs/crores as stated in the document

    **EVALUATION CRITERIA:**
    - Extract the gross cost value of intangible assets
    - If multiple intangible asset categories exist, sum them up
    - If no intangible assets found, return 0
    - Report the amount as stated in the Balance Sheet

    Return STRICTLY in this format:
    - First line: [Numeric value only, e.g., "1500000" for ₹15 lakhs]
    - Second line: "Intangible Assets Cost: ₹[amount] [unit - lakhs/crores]"
    - Third line: "Details: [Brief description of what intangible assets were found]"
    - Fourth line: "Location: [Where in Balance Sheet this was found]"

    **EXAMPLE RESPONSES:**

    **If Intangible Assets Found:**
    2500000
    Intangible Assets Cost: ₹25.00 lakhs
    Details: Computer software and licenses constituting the intangible assets
    Location: Non-current assets section under "Intangible Assets" line item

    **If No Intangible Assets:**
    0
    Intangible Assets Cost: ₹0.00
    Details: No intangible assets reported in the Balance Sheet
    Location: Intangible assets line item absent or shows nil value
  `,
  variables: []
},

// CARO Clause (i)(a)(B) Intangible Assets Check
caro_clause_i_a_B_intangible: {
  key: 'caro_clause_i_a_B_intangible',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (i)(a)(B) which specifically relates to maintenance of records for intangible assets.

    **BACKGROUND ON CARO CLAUSE (i)(a)(B):**
    CARO clause (i)(a)(B) addresses whether the company has maintained proper records showing full particulars, including quantitative details and situation of intangible assets.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(i)(a)(B)" or "(i) (a) (B)" in the CARO document
    2. This clause typically addresses:
       - Maintenance of proper records for intangible assets
       - Full particulars of intangible assets
       - Quantitative details and situation of intangible assets
       - Whether records are properly maintained as per Companies Act requirements

    3. Verify that clause (i)(a)(B) is present and properly addresses intangible assets
    4. Check if the clause indicates compliance or non-compliance with record maintenance

    **EVALUATION CRITERIA:**
    - If clause (i)(a)(B) is present and addresses intangible assets → Answer "Yes"
    - If clause (i)(a)(B) is missing → Answer "No"
    - If clause (i)(a)(B) is present but doesn't address intangible assets → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (i)(a)(B) is properly present, "No" if missing or inadequate
    - Second line: "Clause (i)(a)(B) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Intangible Assets Reference: [Present/Absent] - [brief description]"
    - Fourth line: "Records Maintenance: [Proper/Improper/Not Specified]"
    - Fifth line: "Compliance Indication: [Compliant/Non-Compliant/Not Specified]"
    - Sixth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **If Clause (i)(a)(B) Present and Compliant:**
    Yes
    Clause (i)(a)(B) Status: Present and Complete
    Intangible Assets Reference: Present - Addresses maintenance of records for intangible assets
    Records Maintenance: Proper
    Compliance Indication: Compliant
    Recommendation: CARO clause (i)(a)(B) demonstrates proper maintenance of intangible assets records. This ensures compliance with Companies Act requirements for detailed record-keeping of intangible assets, providing transparency in asset management and supporting audit trail requirements.

    **If Clause (i)(a)(B) Missing:**
    No
    Clause (i)(a)(B) Status: Missing
    Intangible Assets Reference: Absent - Clause not found in CARO
    Records Maintenance: Not Specified
    Compliance Indication: Not Specified
    Recommendation: The absence of CARO clause (i)(a)(B) represents a compliance gap in intangible assets record maintenance reporting. When intangible assets exist, this clause is mandatory under CARO 2020 to confirm proper maintenance of detailed records. The audit firm must include clause (i)(a)(B) with appropriate commentary on intangible assets record-keeping compliance.
  `,
  variables: []
},

// ===========================================
// Balance sheet and caro PPE prompts for the Intangible Assets Cost and caro Clause (i)(a)(A) and clause (i)(b)
// ===========================================
// Balance Sheet PPE/Investment Property Cost Check
bs_ppe_investment_property_cost: {
  key: 'bs_ppe_investment_property_cost',
  template: `
    Analyze this Balance Sheet PDF to extract the total cost/gross value of PPE (Property, Plant & Equipment), Investment Property, and Non-current assets held for sale.

    **SPECIFIC INSTRUCTIONS:**
    1. Look for these sections in the Balance Sheet:
       - "Property, Plant and Equipment" or "PPE"
       - "Investment Property"
       - "Non-current assets held for sale"
       - "Fixed Assets"
       - "Tangible Assets"
       - "Land and Buildings"
       - "Plant and Machinery"
       - "Furniture and Fixtures"
       - "Vehicles"

    2. Extract the total cost/gross value (before depreciation) of these assets
    3. Look in both current year and previous year columns
    4. Sum up all relevant asset categories
    5. Amount should be in lakhs/crores as stated in the document

    **EVALUATION CRITERIA:**
    - Extract the gross cost value of all PPE, Investment Property, and Non-current assets held for sale
    - If multiple asset categories exist, sum them up
    - If no such assets found, return 0
    - Report the total amount as stated in the Balance Sheet

    Return STRICTLY in this format:
    - First line: [Numeric value only, e.g., "5000000" for ₹50 lakhs]
    - Second line: "PPE/Investment Property Cost: ₹[amount] [unit - lakhs/crores]"
    - Third line: "Details: [Brief description of what assets were included]"
    - Fourth line: "Location: [Where in Balance Sheet this was found]"

    **EXAMPLE RESPONSES:**

    **If PPE/Investment Property Found:**
    12500000
    PPE/Investment Property Cost: ₹125.00 lakhs
    Details: Property, plant & equipment including land, buildings, plant & machinery, and investment property
    Location: Non-current assets section under "Property, Plant and Equipment" and "Investment Property" line items

    **If No PPE/Investment Property:**
    0
    PPE/Investment Property Cost: ₹0.00
    Details: No PPE, investment property, or non-current assets held for sale reported
    Location: Fixed assets line items absent or show nil values
  `,
  variables: []
},

// CARO Clause (i)(a)(A) PPE Records Maintenance Check
caro_clause_i_a_A_ppe: {
  key: 'caro_clause_i_a_A_ppe',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (i)(a)(A) which specifically relates to maintenance of records for Property, Plant & Equipment.

    **BACKGROUND ON CARO CLAUSE (i)(a)(A):**
    CARO clause (i)(a)(A) addresses whether the company has maintained proper records showing full particulars, including quantitative details and situation of Property, Plant and Equipment.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(i)(a)(A)" or "(i) (a) (A)" in the CARO document
    2. This clause typically addresses:
       - Maintenance of proper records for PPE
       - Full particulars of fixed assets
       - Quantitative details and situation of assets
       - Whether records are properly maintained as per Companies Act requirements

    3. Verify that clause (i)(a)(A) is present and properly addresses PPE/fixed assets
    4. Check if the clause indicates compliance or non-compliance with record maintenance

    **EVALUATION CRITERIA:**
    - If clause (i)(a)(A) is present and addresses PPE/fixed assets → Answer "Yes"
    - If clause (i)(a)(A) is missing → Answer "No"
    - If clause (i)(a)(A) is present but doesn't address PPE → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (i)(a)(A) is properly present, "No" if missing or inadequate
    - Second line: "Clause (i)(a)(A) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "PPE Records Reference: [Present/Absent] - [brief description]"
    - Fourth line: "Records Maintenance: [Proper/Improper/Not Specified]"
    - Fifth line: "Compliance Indication: [Compliant/Non-Compliant/Not Specified]"
    - Sixth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **If Clause (i)(a)(A) Present and Compliant:**
    Yes
    Clause (i)(a)(A) Status: Present and Complete
    PPE Records Reference: Present - Addresses maintenance of records for Property, Plant & Equipment
    Records Maintenance: Proper
    Compliance Indication: Compliant
    Recommendation: CARO clause (i)(a)(A) demonstrates proper maintenance of PPE records. This ensures compliance with Companies Act requirements for detailed record-keeping of fixed assets, providing transparency in asset management and supporting audit trail requirements.

    **If Clause (i)(a)(A) Missing:**
    No
    Clause (i)(a)(A) Status: Missing
    PPE Records Reference: Absent - Clause not found in CARO
    Records Maintenance: Not Specified
    Compliance Indication: Not Specified
    Recommendation: The absence of CARO clause (i)(a)(A) represents a compliance gap in PPE record maintenance reporting. When significant fixed assets exist, this clause is mandatory under CARO 2020 to confirm proper maintenance of detailed records. The audit firm must include clause (i)(a)(A) with appropriate commentary on asset record-keeping compliance.
  `,
  variables: []
},

// CARO Clause (i)(b) Physical Verification Check
caro_clause_i_b_physical_verification: {
  key: 'caro_clause_i_b_physical_verification',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (i)(b) which specifically relates to physical verification of Property, Plant & Equipment.

    **BACKGROUND ON CARO CLAUSE (i)(b):**
    CARO clause (i)(b) addresses whether the company has a program of physical verification of Property, Plant and Equipment to cover all the assets in a phased manner which, in our opinion, is reasonable having regard to the size of the company and the nature of its assets.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(i)(b)" or "(i) (b)" in the CARO document
    2. This clause typically addresses:
       - Physical verification program for PPE
       - Coverage of all assets in phased manner
       - Reasonableness of verification program
       - Whether physical verification is adequate

    3. Verify that clause (i)(b) is present and properly addresses physical verification
    4. Check if the clause indicates compliance or non-compliance with verification requirements

    **EVALUATION CRITERIA:**
    - If clause (i)(b) is present and addresses physical verification → Answer "Yes"
    - If clause (i)(b) is missing → Answer "No"
    - If clause (i)(b) is present but doesn't address verification → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (i)(b) is properly present, "No" if missing or inadequate
    - Second line: "Clause (i)(b) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Physical Verification Reference: [Present/Absent] - [brief description]"
    - Fourth line: "Verification Program: [Adequate/Inadequate/Not Specified]"
    - Fifth line: "Compliance Indication: [Compliant/Non-Compliant/Not Specified]"
    - Sixth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **If Clause (i)(b) Present and Compliant:**
    Yes
    Clause (i)(b) Status: Present and Complete
    Physical Verification Reference: Present - Addresses physical verification program for PPE
    Verification Program: Adequate
    Compliance Indication: Compliant
    Recommendation: CARO clause (i)(b) demonstrates adequate physical verification program for PPE. This ensures proper asset management through systematic physical verification, providing assurance on asset existence and condition, and supporting effective internal controls over fixed assets.

    **If Clause (i)(b) Missing:**
    No
    Clause (i)(b) Status: Missing
    Physical Verification Reference: Absent - Clause not found in CARO
    Verification Program: Not Specified
    Compliance Indication: Not Specified
    Recommendation: The absence of CARO clause (i)(b) represents a significant compliance gap in physical verification reporting. When substantial fixed assets exist, this clause is mandatory under CARO 2020 to confirm adequate physical verification procedures. The audit firm must include clause (i)(b) with appropriate commentary on the company's physical verification program and its adequacy.
  `,
  variables: []
},

// ===========================================
// Balance sheet inventory and Caro Annexure (ii)(a)
// ===========================================
// Add these two prompts to your MULTI_DOCUMENT_PROMPTS object in multiDocumentPrompts.ts

// Balance Sheet Inventory Value Check
bs_inventory_value: {
  key: 'bs_inventory_value',
  template: `
    Analyze this Balance Sheet PDF to extract the total value of inventory/stock.

    **SPECIFIC INSTRUCTIONS:**
    1. Look for these sections in the Balance Sheet:
       - "Inventories" or "Inventory"
       - "Stock in Trade"
       - "Raw Materials"
       - "Work in Progress" or "WIP"
       - "Finished Goods"
       - "Stock"
       - "Stores and Spares"
       - "Consumables"

    2. Extract the total value of all inventory items
    3. Look in both current assets and current year columns
    4. Sum up all relevant inventory categories if multiple exist
    5. Amount should be in lakhs/crores as stated in the document
    6. If inventory line item exists but shows zero value, return 0
    7. If no inventory line item exists at all, return 0

    **EVALUATION CRITERIA:**
    - Extract the total value of all inventories/stock
    - If multiple inventory categories exist, sum them up
    - If inventory shows zero or nil value, return 0
    - If no inventory found anywhere, return 0
    - Report the amount as stated in the Balance Sheet

    Return STRICTLY in this format:
    - First line: [Numeric value only, e.g., "750000" for ₹7.5 lakhs]
    - Second line: "Inventory Value: ₹[amount] [unit - lakhs/crores]"
    - Third line: "Details: [Brief description of what inventory items were found]"
    - Fourth line: "Location: [Where in Balance Sheet this was found]"

    **EXAMPLE RESPONSES:**

    **If Inventory Found:**
    2750000
    Inventory Value: ₹27.50 lakhs
    Details: Raw materials, work-in-progress, and finished goods constituting total inventory
    Location: Current assets section under "Inventories" line item

    **If Inventory Shows Zero Value:**
    0
    Inventory Value: ₹0.00
    Details: Inventory line item present but shows nil/zero value
    Location: Current assets section shows "Inventories: Nil" or zero amount

    **If No Inventory Found:**
    0
    Inventory Value: ₹0.00
    Details: No inventory or stock items reported in the Balance Sheet
    Location: Inventory/stock line items absent from current assets
  `,
  variables: []
},

// CARO Clause (ii)(a) Inventory Verification Check - Enhanced Version
caro_clause_ii_a_inventory: {
  key: 'caro_clause_ii_a_inventory',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (ii)(a) which specifically relates to inventory verification procedures.

    **BACKGROUND ON CARO CLAUSE (ii)(a):**
    CARO clause (ii)(a) addresses whether the management has conducted physical verification of inventory at reasonable intervals during the year and whether any material discrepancies were noticed on such verification and whether they have been properly dealt with in the books of account.

    **MANDATORY REQUIREMENT:**
    This clause MUST be present if the company has inventory value more than 0 (zero) in the financial statements.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(ii)(a)" or "(ii) (a)" in the CARO document
    2. This clause typically addresses:
       - Physical verification of inventory at reasonable intervals
       - Whether verification was conducted during the year
       - Any material discrepancies noticed during verification
       - Whether discrepancies were properly dealt with in books
       - **WRITTEN CONFIRMATIONS obtained from management regarding inventory verification procedures**

    3. Verify that clause (ii)(a) is present and properly addresses inventory verification
    4. Check if the clause indicates compliance or non-compliance with verification procedures
    5. **CRITICAL CHECK:** Verify if written confirmations have been obtained from management
       - If written confirmations NOT obtained → Mark as Non-Compliant
       - Written confirmations are MANDATORY for proper CARO compliance

    **EVALUATION CRITERIA:**
    - If clause (ii)(a) is present, addresses inventory verification AND written confirmations obtained → Answer "Yes"
    - If clause (ii)(a) is missing → Answer "No"
    - If clause (ii)(a) is present but doesn't address inventory properly → Answer "No"
    - If clause (ii)(a) is present but written confirmations NOT obtained → Answer "No" (Non-Compliant)

    Return STRICTLY in this format:
    - First line: "Yes" if clause (ii)(a) is properly present with written confirmations, "No" if missing, inadequate, or confirmations not obtained
    - Second line: "Clause (ii)(a) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Inventory Verification Reference: [Present/Absent] - [brief description]"
    - Fourth line: "Physical Verification: [Conducted/Not Conducted/Not Specified]"
    - Fifth line: "Written Confirmations: [Obtained/Not Obtained/Not Specified]"
    - Sixth line: "Discrepancies Handling: [Proper/Improper/Not Applicable/Not Specified]"
    - Seventh line: "Compliance Indication: [Compliant/Non-Compliant/Not Specified]"
    - Eighth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **If Clause (ii)(a) Present, Compliant with Written Confirmations:**
    Yes
    Clause (ii)(a) Status: Present and Complete
    Inventory Verification Reference: Present - Addresses physical verification of inventory
    Physical Verification: Conducted
    Written Confirmations: Obtained
    Discrepancies Handling: Proper
    Compliance Indication: Compliant
    Recommendation: CARO clause (ii)(a) demonstrates adequate inventory verification procedures with proper written confirmations obtained. This ensures comprehensive stock management through systematic physical verification at reasonable intervals, documented management confirmations, and proper discrepancy resolution, providing strong assurance on inventory existence and valuation.

    **If Clause (ii)(a) Missing:**
    No
    Clause (ii)(a) Status: Missing
    Inventory Verification Reference: Absent - Clause not found in CARO
    Physical Verification: Not Specified
    Written Confirmations: Not Specified
    Discrepancies Handling: Not Specified
    Compliance Indication: Not Specified
    Recommendation: The absence of CARO clause (ii)(a) represents a significant compliance gap in inventory verification reporting. When inventory value is more than 0, this clause is MANDATORY under CARO 2020. The audit firm must include clause (ii)(a) with appropriate commentary on inventory verification procedures, written management confirmations, and discrepancy handling effectiveness.

    **If Clause (ii)(a) Present but Written Confirmations Not Obtained:**
    No
    Clause (ii)(a) Status: Present but Incomplete
    Inventory Verification Reference: Present - Addresses physical verification but lacks confirmation documentation
    Physical Verification: Conducted
    Written Confirmations: Not Obtained
    Discrepancies Handling: Proper
    Compliance Indication: Non-Compliant
    Recommendation: CARO clause (ii)(a) is present but NON-COMPLIANT due to absence of written confirmations from management. Written confirmations regarding inventory verification procedures are MANDATORY for CARO compliance. The audit firm must obtain proper written confirmations from management about inventory verification processes, intervals, and discrepancy handling procedures to achieve full compliance.

    **If Clause (ii)(a) Present but Non-Compliant on Multiple Aspects:**
    No
    Clause (ii)(a) Status: Present but Incomplete
    Inventory Verification Reference: Present - Addresses physical verification issues
    Physical Verification: Conducted
    Written Confirmations: Not Obtained
    Discrepancies Handling: Improper
    Compliance Indication: Non-Compliant
    Recommendation: CARO clause (ii)(a) indicates multiple inventory verification deficiencies requiring immediate management attention. Critical issues include: (1) Absence of written confirmations from management - MANDATORY requirement, (2) Improper handling of material discrepancies in books of account. Enhanced inventory controls, proper documentation procedures, and comprehensive reconciliation processes must be implemented to achieve CARO compliance.
  `,
  variables: []
},

// ===========================================
// Balance sheet current assets and liabilities clause (ix)(b) for Current Assets vs Current Liabilities
// ===========================================

// Balance Sheet Current Assets vs Current Liabilities Analysis
bs_current_assets_vs_liabilities: {
  key: 'bs_current_assets_vs_liabilities',
  template: `
    Analyze this Balance Sheet PDF to extract and compare Current Assets and Current Liabilities values.

    **SPECIFIC INSTRUCTIONS:**
    1. Find Current Assets section including:
       - Cash and Cash Equivalents
       - Short-term Investments
       - Trade Receivables
       - Inventories
       - Other Current Assets
       - Prepaid Expenses

    2. Find Current Liabilities section including:
       - Trade Payables
       - Short-term Borrowings
       - Other Current Liabilities
       - Short-term Provisions
       - Current maturities of long-term debt

    3. Extract the total amounts for both Current Assets and Current Liabilities
    4. Compare the values and determine which is greater
    5. Calculate the difference between them
    6. Amount should be in lakhs/crores as stated in the document

    **EVALUATION CRITERIA:**
    - Compare Current Assets vs Current Liabilities
    - Determine which amount is higher
    - Calculate the working capital (Current Assets - Current Liabilities)
    - Provide clear analysis of the liquidity position

    Return STRICTLY in this format:
    - First line: EXACTLY one of these three words only: "assets_greater" OR "liabilities_greater" OR "equal"
    - Second line: "Current Assets: ₹[amount] [unit]"
    - Third line: "Current Liabilities: ₹[amount] [unit]"
    - Fourth line: "Working Capital: ₹[difference] [unit] ([Positive/Negative])"
    - Fifth line: "Analysis: [Brief explanation of the ratio and its implications]"
    - Sixth line: "Location: [Where these figures were found in Balance Sheet]"

    **CRITICAL: The first line must contain ONLY the analysis result word, nothing else.**

    **EXAMPLE RESPONSES:**

    **If Current Assets > Current Liabilities:**
    assets_greater
    Current Assets: ₹150.00 lakhs
    Current Liabilities: ₹120.00 lakhs
    Working Capital: ₹30.00 lakhs (Positive)
    Analysis: Company has positive working capital indicating good short-term liquidity position
    Location: Current Assets and Current Liabilities sections in the Balance Sheet

    **If Current Liabilities > Current Assets:**
    liabilities_greater
    Current Assets: ₹80.00 lakhs
    Current Liabilities: ₹100.00 lakhs
    Working Capital: ₹-20.00 lakhs (Negative)
    Analysis: Company has negative working capital indicating potential liquidity concerns
    Location: Current Assets and Current Liabilities sections in the Balance Sheet

    **If Current Assets = Current Liabilities:**
    equal
    Current Assets: ₹100.00 lakhs
    Current Liabilities: ₹100.00 lakhs
    Working Capital: ₹0.00 lakhs (Neutral)
    Analysis: Company has balanced current assets and liabilities with neutral working capital
    Location: Current Assets and Current Liabilities sections in the Balance Sheet
  `,
  variables: []
},

// CARO Clause (ix)(d) Funds Utilization Check
caro_clause_ix_d_funds_utilization: {
  key: 'caro_clause_ix_d_funds_utilization',
  template: `
    Analyze this CARO Annexure PDF to check clause (ix)(d) which relates to utilization of short-term funds for long-term purposes.

    **BACKGROUND ON CARO CLAUSE (ix)(d):**
    CARO clause (ix)(d) addresses whether funds raised on short term basis have been utilized for long term purposes by the company.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(ix)(d)" or "(ix) (d)" in the CARO document
    2. This clause typically contains one of these statements:
       - "no funds raised on short term basis have been utilized for long term purposes"
       - "funds raised on short term basis have been utilized for long term purposes"
       - Specific details about short-term fund utilization

    3. Extract the exact wording or meaning of what the clause states
    4. Determine if it indicates funds were utilized for long-term purposes or not

    **EVALUATION CRITERIA:**
    - If clause states "no funds utilized for long-term" → Answer "no_funds_utilized"
    - If clause states "funds utilized for long-term" → Answer "funds_utilized"
    - If clause is not applicable or not mentioned → Answer "not_applicable"
    - If clause exists but content unclear → Answer "not_specified"

    Return STRICTLY in this format:
    - First line: EXACTLY one of these four words only: "no_funds_utilized" OR "funds_utilized" OR "not_applicable" OR "not_specified"
    - Second line: "Clause (ix)(d) Status: [Present and Complete/Present but Incomplete/Missing/Not Applicable]"
    - Third line: "Funds Utilization Statement: [Exact quote or paraphrase of the clause content]"
    - Fourth line: "Short-term to Long-term Usage: [Yes/No/Not Specified]"
    - Fifth line: "Compliance Indication: [Compliant/Non-Compliant/Not Applicable]"
    - Sixth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **If No Funds Utilized for Long-term:**
    no_funds_utilized
    Clause (ix)(d) Status: Present and Complete
    Funds Utilization Statement: "no funds raised on short term basis have been utilized for long term purposes"
    Short-term to Long-term Usage: No
    Compliance Indication: Compliant
    Recommendation: CARO clause (ix)(d) confirms appropriate fund management with no short-term funds diverted for long-term purposes. This indicates prudent liquidity management and proper matching of fund sources with their intended uses, supporting healthy working capital management.

    **If Funds Utilized for Long-term:**
    funds_utilized
    Clause (ix)(d) Status: Present and Complete
    Funds Utilization Statement: "funds raised on short term basis have been utilized for long term purposes to the extent of ₹XX lakhs"
    Short-term to Long-term Usage: Yes
    Compliance Indication: Non-Compliant
    Recommendation: CARO clause (ix)(d) indicates short-term funds have been utilized for long-term purposes, which may indicate working capital management concerns. The company should review its funding strategy to ensure appropriate matching of fund tenure with asset deployment and consider long-term financing for long-term requirements.

    **If Clause Missing:**
    not_specified
    Clause (ix)(d) Status: Missing
    Funds Utilization Statement: Clause (ix)(d) not found in CARO
    Short-term to Long-term Usage: Not Specified
    Compliance Indication: Non-Compliant
    Recommendation: The absence of CARO clause (ix)(d) represents a compliance gap. This clause is mandatory under CARO 2020 to address short-term fund utilization for long-term purposes. The audit firm must include clause (ix)(d) with appropriate commentary on the company's fund utilization practices and working capital management.

    **If Not Applicable:**
    not_applicable
    Clause (ix)(d) Status: Not Applicable
    Funds Utilization Statement: "Not applicable - company has not raised funds on short term basis"
    Short-term to Long-term Usage: Not Applicable
    Compliance Indication: Compliant
    Recommendation: CARO clause (ix)(d) appropriately indicates non-applicability as the company has not raised short-term funds. This suggests the company's financing structure does not involve short-term borrowings that could potentially be mismatched with long-term asset deployment.
  `,
  variables: []
},

// Add these two prompts to your MULTI_DOCUMENT_PROMPTS object in multiDocumentPrompts.ts

// CSR Unspent Amount Check
csr_unspent_amount_check: {
  key: 'csr_unspent_amount_check',
  template: `
    Analyze this Notes to Financial Statements PDF to extract CSR (Corporate Social Responsibility) unspent amount information.

    **SPECIFIC INSTRUCTIONS:**
    1. Look for CSR-related notes or disclosures, typically found in:
       - "Corporate Social Responsibility" note
       - "CSR Activities" section
       - "Unspent CSR Amount" disclosures
       - Notes about Section 135 of Companies Act
       - CSR expenditure details

    2. Find specifically the "unspent amount on CSR activities other than ongoing projects"
    3. Look for information about:
       - Amount unspent at year end
       - Whether amount has been transferred to Unspent CSR Account
       - Any amounts not yet transferred
       - CSR obligations and actual spending

    4. Extract the total unspent amount (not the total CSR expenditure)
    5. Amount should be in lakhs/crores as stated in the document

    **EVALUATION CRITERIA:**
    - Extract the unspent amount on CSR activities (not total CSR spent)
    - If no unspent amount mentioned or CSR not applicable, return 0
    - If CSR obligations fully met, return 0
    - Report the unspent amount as stated in the Notes

    Return STRICTLY in this format:
    - First line: [Numeric value only, e.g., "250000" for ₹2.5 lakhs unspent]
    - Second line: "CSR Unspent Amount: ₹[amount] [unit - lakhs/crores]"
    - Third line: "Details: [Brief description of CSR unspent status]"
    - Fourth line: "Transfer Status: [Transferred/Not Transferred/Not Applicable]"
    - Fifth line: "Location: [Where in Notes this was found]"

    **EXAMPLE RESPONSES:**

    **If CSR Unspent Amount Found:**
    500000
    CSR Unspent Amount: ₹5.00 lakhs
    Details: Amount unspent on CSR activities other than ongoing projects at year end
    Transfer Status: Not Transferred
    Location: Note on Corporate Social Responsibility under "Unspent CSR Amount" section

    **If No CSR Unspent Amount:**
    0
    CSR Unspent Amount: ₹0.00
    Details: No unspent amount on CSR activities - all CSR obligations fulfilled
    Transfer Status: Not Applicable
    Location: CSR note indicates full compliance with spending requirements

    **If CSR Not Applicable:**
    0
    CSR Unspent Amount: ₹0.00
    Details: CSR provisions not applicable to the company
    Transfer Status: Not Applicable
    Location: CSR note indicates company not covered under Section 135
  `,
  variables: []
},

// CARO Clause (xx) CSR Check
// SOLUTION: Replace the caro_clause_xx_csr prompt in multiDocumentPrompts.ts

caro_clause_xx_csr: {
  key: 'caro_clause_xx_csr',
  template: `
    Analyze this CARO Annexure PDF to check clause (xx) which relates to Corporate Social Responsibility compliance.

    **BACKGROUND ON CARO CLAUSE (xx):**
    CARO clause (xx) addresses CSR compliance, and has sub-clauses:
    - Clause (xx): General CSR compliance
    - Clause (xx)(a): Whether unspent CSR amount has been transferred to Unspent CSR Account
    - Clause (xx)(b): Whether company has transferred required amount to specified funds within time

    **CRITICAL: Your first line response must be EXACTLY one of these four options:**
    - "clause_xx_only" (if only main clause (xx) exists, no sub-clauses)
    - "clause_xx_a_and_b" (if both sub-clauses (a) and (b) are present)
    - "not_applicable" (if CSR is not applicable to the company)
    - "missing" (if clause (xx) is completely absent)

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(xx)" in the CARO document
    2. Check if sub-clauses (xx)(a) and (xx)(b) are ACTUALLY PRESENT as functional clauses
    3. Determine the exact structure of clause (xx) reporting

    **ANALYSIS LOGIC:**
    - If you find ONLY clause (xx) with NO sub-clauses (a) or (b) → "clause_xx_only"
    - If you find clause (xx) AND both sub-clauses (a) AND (b) are PRESENT → "clause_xx_a_and_b"
    - If clause states "not applicable" or company not subject to CSR → "not_applicable"
    - If no clause (xx) found at all → "missing"

    **IMPORTANT: When reporting sub-clause status, be very clear:**
    - Use "Present" only if the sub-clause actually exists and addresses CSR matters
    - Use "Absent" if the sub-clause is not there or explicitly not applicable

    Return STRICTLY in this format:
    - First line: EXACTLY one word from: "clause_xx_only" OR "clause_xx_a_and_b" OR "not_applicable" OR "missing"
    - Second line: "Clause (xx) Status: [Present/Present with sub-clauses/Not Applicable/Missing]"
    - Third line: "Sub-clause (a) Status: [Present/Absent/Not Applicable]"
    - Fourth line: "Sub-clause (b) Status: [Present/Absent/Not Applicable]"
    - Fifth line: "CSR Compliance: [Compliant/Non-Compliant/Not Applicable]"
    - Sixth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **Example 1 - Only Main Clause (Most Common for ₹0 unspent):**
    clause_xx_only
    Clause (xx) Status: Present
    Sub-clause (a) Status: Absent
    Sub-clause (b) Status: Absent
    CSR Compliance: Compliant
    Recommendation: CARO clause (xx) indicates CSR compliance without unspent amounts. The absence of sub-clauses (a) and (b) suggests no unspent CSR amounts requiring specific transfer disclosures, indicating full CSR obligation fulfillment during the year.

    **Example 2 - Both Sub-clauses Present:**
    clause_xx_a_and_b
    Clause (xx) Status: Present with sub-clauses
    Sub-clause (a) Status: Present
    Sub-clause (b) Status: Present
    CSR Compliance: Requires Review
    Recommendation: CARO clause (xx) with sub-clauses (a) and (b) indicates unspent CSR amounts requiring specific disclosures.

    **Example 3 - Not Applicable:**
    not_applicable
    Clause (xx) Status: Not Applicable
    Sub-clause (a) Status: Not Applicable
    Sub-clause (b) Status: Not Applicable
    CSR Compliance: Not Applicable
    Recommendation: CARO clause (xx) is not applicable as the company does not fall under CSR requirements.
  `,
  variables: []
},


// ===========================================
// BALANCE SHEET + NOTES + CARO FIXED DEPOSITS PROMPTS
// ===========================================

// Balance Sheet Fixed Deposits in Borrowings Check
bs_borrowings_fixed_deposits_check: {
  key: 'bs_borrowings_fixed_deposits_check',
  template: `
    Analyze this Balance Sheet PDF to check if borrowings under non-current liabilities or current liabilities include any fixed deposits.

    **SPECIFIC INSTRUCTIONS:**
    1. Look for borrowings sections in both:
       - "Non-current Liabilities" section
       - "Current Liabilities" section

    2. Check for any mention of "fixed deposits" in borrowings including:
       - "Fixed deposits from public"
       - "Fixed deposits from directors/shareholders"
       - "Fixed deposits" as part of borrowings
       - "Deposits" in borrowings
       - "Public deposits"
       - "Term deposits"
       - Any borrowings specifically labeled as deposits

    3. Look for line items that combine borrowings with deposits such as:
       - "Borrowings and deposits"
       - "Loans and deposits"
       - "Bank borrowings and fixed deposits"

    4. Exclude these items (not considered fixed deposits in borrowings):
       - Security deposits received (unless specifically called fixed deposits)
       - Margin deposits
       - Refundable deposits from customers
       - Statutory deposits

    **EVALUATION CRITERIA:**
    - If ANY fixed deposits are found as part of borrowings → Answer "Yes"
    - If borrowings exist but no fixed deposits mentioned → Answer "No"
    - If no borrowings at all → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if fixed deposits are present in borrowings, "No" if not present
    - Second line: "Fixed Deposits in Borrowings: [Present/Not Present]"
    - Third line: "Details: [Specific line items where fixed deposits in borrowings were found, or 'No fixed deposits found in borrowings']"
    - Fourth line: "Location: [Where in Balance Sheet this was found - Non-current/Current liabilities]"
    - Fifth line: "Amount: [Total amount if specified, or 'Not specified']"

    **EXAMPLE RESPONSES:**

    **If Fixed Deposits Found in Borrowings:**
    Yes
    Fixed Deposits in Borrowings: Present
    Details: "Fixed deposits from public" under non-current liabilities and "Current maturities of fixed deposits" under current liabilities
    Location: Non-current liabilities and Current liabilities sections
    Amount: ₹25.00 lakhs (non-current) + ₹5.00 lakhs (current)

    **If No Fixed Deposits in Borrowings:**
    No
    Fixed Deposits in Borrowings: Not Present
    Details: Borrowings include term loans, working capital loans, but no fixed deposits mentioned
    Location: Both non-current and current liabilities sections reviewed
    Amount: Not applicable

    **If No Borrowings at All:**
    No
    Fixed Deposits in Borrowings: Not Present
    Details: No borrowings reported in either non-current or current liabilities
    Location: Liabilities sections show no borrowings
    Amount: Not applicable
  `,
  variables: []
},

// Notes to Accounts Fixed Deposits Details Check
notes_fixed_deposits_borrowings_details: {
  key: 'notes_fixed_deposits_borrowings_details',
  template: `
    Analyze this Notes to Financial Statements PDF to find detailed disclosure about fixed deposits included in borrowings.

    **SPECIFIC INSTRUCTIONS:**
    1. Look for notes related to:
       - "Borrowings" note
       - "Non-current borrowings" note
       - "Current borrowings" note
       - "Fixed deposits" note
       - "Deposits from public" note
       - "Terms and conditions of borrowings"

    2. Check for specific disclosures about fixed deposits including:
       - Details of fixed deposits accepted from public
       - Terms and conditions of fixed deposits
       - Interest rates on fixed deposits
       - Maturity details of fixed deposits
       - Renewal terms for fixed deposits
       - Any defaults or non-compliance related to fixed deposits

    3. Look for compliance statements such as:
       - "Fixed deposits accepted in accordance with Section 73-76"
       - "Company has complied with deposit acceptance norms"
       - "Fixed deposits accepted under exemptions"

    4. Check for any mention of regulatory approvals or RBI permissions for accepting deposits

    **EVALUATION CRITERIA:**
    - If detailed fixed deposits disclosure is found → Answer "Yes"
    - If fixed deposits mentioned but no details provided → Answer "No"
    - If no fixed deposits mentioned at all → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if detailed fixed deposits disclosure is present, "No" if absent
    - Second line: "Fixed Deposits Disclosure: [Present with Details/Present but Limited/Absent]"
    - Third line: "Details Found: [Summary of what fixed deposits details were disclosed]"
    - Fourth line: "Compliance Statement: [Present/Absent] - [brief description if present]"
    - Fifth line: "Regulatory References: [Sections 73-76 mentioned/RBI approvals mentioned/None]"
    - Sixth line: "Location: [Which note number or section contains the disclosure]"

    **EXAMPLE RESPONSES:**

    **If Detailed Fixed Deposits Disclosure Found:**
    Yes
    Fixed Deposits Disclosure: Present with Details
    Details Found: Fixed deposits of ₹30 lakhs accepted from public at 8% interest, maturity 3 years, renewable at mutual consent
    Compliance Statement: Present - "Company has complied with provisions of Sections 73 to 76 of Companies Act"
    Regulatory References: Sections 73-76 mentioned, RBI exemption referenced
    Location: Note 15 - Non-current Borrowings and Note 16 - Current Borrowings

    **If Limited Fixed Deposits Information:**
    No
    Fixed Deposits Disclosure: Present but Limited
    Details Found: Fixed deposits mentioned in borrowings note but no detailed terms provided
    Compliance Statement: Absent - No compliance statement found
    Regulatory References: None mentioned
    Location: Note 12 - Borrowings (brief mention only)

    **If No Fixed Deposits Disclosure:**
    No
    Fixed Deposits Disclosure: Absent
    Details Found: No mention of fixed deposits in any borrowings or deposits related notes
    Compliance Statement: Not applicable
    Regulatory References: None
    Location: Fixed deposits not mentioned in any notes
  `,
  variables: []
},

// CARO Clause (v) Fixed Deposits Check
caro_clause_v_fixed_deposits: {
  key: 'caro_clause_v_fixed_deposits',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (v) which specifically relates to acceptance of deposits.

    **BACKGROUND ON CARO CLAUSE (v):**
    CARO clause (v) addresses whether the company has accepted any deposits within the meaning of Sections 73 to 76 or any other relevant provisions of the Companies Act, 2013 and the rules made thereunder, where applicable.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(v)" or "(v)" in the CARO document
    2. This clause typically addresses:
       - Whether company has accepted deposits under Sections 73-76
       - Compliance with deposit acceptance norms
       - Any violations or non-compliance with deposit rules
       - Whether deposits are exempt or covered under specific provisions

    3. Verify that clause (v) is present and properly addresses deposit acceptance
    4. Check if the clause indicates compliance or non-compliance with deposit provisions

    **EVALUATION CRITERIA:**
    - If clause (v) is present and addresses deposit acceptance → Answer "Yes"
    - If clause (v) is missing → Answer "No"
    - If clause (v) is present but doesn't address deposits properly → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (v) is properly present, "No" if missing or inadequate
    - Second line: "Clause (v) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Deposits Reference: [Present/Absent] - [brief description of what clause states]"
    - Fourth line: "Sections 73-76 Reference: [Present/Absent]"
    - Fifth line: "Compliance Indication: [Compliant/Non-Compliant/Not Applicable/Not Specified]"
    - Sixth line: "Recommendation: [Professional guidance based on findings]"

    **EXAMPLE RESPONSES:**

    **If Clause (v) Present and Compliant:**
    Yes
    Clause (v) Status: Present and Complete
    Deposits Reference: Present - Addresses acceptance of deposits under Companies Act provisions
    Sections 73-76 Reference: Present
    Compliance Indication: Compliant
    Recommendation: CARO clause (v) demonstrates proper compliance framework for deposit acceptance under Sections 73 to 76 of the Companies Act, 2013. This comprehensive disclosure ensures stakeholder transparency regarding deposit acceptance activities, maintains regulatory adherence, and provides audit trail for deposit compliance verification.

    **If Clause (v) Missing:**
    No
    Clause (v) Status: Missing
    Deposits Reference: Absent - Clause not found in CARO
    Sections 73-76 Reference: Absent
    Compliance Indication: Not Specified
    Recommendation: The absence of CARO clause (v) represents a significant regulatory gap in deposit acceptance reporting. This clause is mandatory under the Companies (Auditor's Report) Order, 2020 to address whether the company has accepted deposits under Sections 73-76 of the Companies Act. The audit firm must include clause (v) with appropriate commentary on deposit acceptance status to ensure complete CARO compliance.

    **If Clause (v) Present - Non-Compliant:**
    Yes
    Clause (v) Status: Present and Complete
    Deposits Reference: Present - Indicates non-compliance with deposit acceptance norms
    Sections 73-76 Reference: Present
    Compliance Indication: Non-Compliant
    Recommendation: CARO clause (v) identifies deposit acceptance compliance deficiencies requiring immediate management attention. Non-compliance with Sections 73-76 of the Companies Act regarding deposit acceptance creates significant regulatory risk. The company should implement corrective measures to ensure full compliance with deposit acceptance norms, obtain necessary approvals, and establish proper deposit management procedures.

    **If Clause (v) Present - Not Applicable:**
    Yes
    Clause (v) Status: Present and Complete
    Deposits Reference: Present - States company has not accepted deposits
    Sections 73-76 Reference: Present
    Compliance Indication: Not Applicable
    Recommendation: CARO clause (v) appropriately indicates the company has not accepted any deposits under Sections 73-76 of the Companies Act, confirming non-applicability of deposit acceptance provisions. This clear disclosure demonstrates the company's funding structure does not involve public deposit acceptance, maintaining transparency in borrowing arrangements.
  `,
  variables: []
},

// ===========================================
// CTRL + F and search for "Clause (viii)" in the CARO document
// ===========================================


// CARO Clause (viii) - Income Tax Raids Check
caro_clause_viii_income_tax: {
  key: 'caro_clause_viii_income_tax',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (viii) which relates to income tax raids, unrecorded income, or tax authority proceedings.

    **BACKGROUND ON CARO CLAUSE (viii):**
    CARO clause (viii) addresses whether the company has surrendered or disclosed any transaction, previously unrecorded in the books of account, in the tax assessments under the Income Tax Act, 1961 (such as, search, survey or any other relevant provisions of the Income Tax Act, 1961).

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(viii)" in the CARO document
    2. This clause typically addresses:
       - Income tax raids or surveys
       - Unrecorded transactions disclosed during tax assessments
       - Search and seizure operations by income tax authorities
       - Any surrendered income during tax proceedings

    3. Extract the complete clause content
    4. Determine if the clause indicates any issues or is clean

    **EVALUATION CRITERIA:**
    - If clause (viii) is present and addresses income tax matters → Answer "Yes"
    - If clause (viii) is missing → Answer "No"
    - If clause (viii) is present but doesn't address income tax properly → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (viii) is properly present, "No" if missing or inadequate
    - Second line: "Clause (viii) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Income Tax Issues: [Found/None/Not Specified]"
    - Fourth line: "Unrecorded Income: [Disclosed/None/Not Specified]"
    - Fifth line: "Clause Content: [Brief summary of what clause states]"
    - Sixth line: "Recommendation: [Professional guidance based on findings]"
  `,
  variables: []
},

// CARO Clause (ix) - Defaults Check
caro_clause_ix_defaults: {
  key: 'caro_clause_ix_defaults',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (ix) which relates to defaults, wilful defaulter status, or fund diversions.

    **BACKGROUND ON CARO CLAUSE (ix):**
    CARO clause (ix) has multiple sub-clauses addressing:
    - (ix)(a): Default in repayment of loans or other borrowings or payment of interest
    - (ix)(b): Whether company is declared wilful defaulter
    - (ix)(c): Term loans were applied for the purpose for which they were raised
    - (ix)(d): Whether funds raised on short term basis have been utilized for long term purposes

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(ix)" and its sub-clauses in the CARO document
    2. Look for mentions of:
       - Defaults in loan repayments
       - Wilful defaulter declarations
       - Fund diversion or misutilization
       - Non-compliance with loan covenants

    3. Extract relevant sub-clause content
    4. Determine compliance status

    **EVALUATION CRITERIA:**
    - If clause (ix) is present and addresses default-related matters → Answer "Yes"
    - If clause (ix) is missing → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (ix) is properly present, "No" if missing
    - Second line: "Clause (ix) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Default Issues: [Found/None/Not Specified]"
    - Fourth line: "Wilful Defaulter: [Yes/No/Not Specified]"
    - Fifth line: "Fund Diversion: [Found/None/Not Specified]"
    - Sixth line: "Clause Summary: [Brief summary of compliance status]"
    - Seventh line: "Recommendation: [Professional guidance based on findings]"
  `,
  variables: []
},

// CARO Clause (x)(b) - Rights Issue Check
caro_clause_x_b_rights_issue: {
  key: 'caro_clause_x_b_rights_issue',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (x)(b) which relates to rights issues of shares or convertible debentures.

    **BACKGROUND ON CARO CLAUSE (x)(b):**
    CARO clause (x)(b) addresses whether the company has raised moneys by way of rights issue of shares or convertible debentures and whether the company has applied the moneys so raised for the purposes for which they were raised.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(x)(b)" in the CARO document
    2. This clause typically addresses:
       - Rights issue of shares during the year
       - Rights issue of convertible debentures
       - Proper utilization of rights issue proceeds
       - Compliance with stated purpose of rights issue

    3. Extract the complete sub-clause content
    4. Determine if proceeds were used for intended purposes

    **EVALUATION CRITERIA:**
    - If clause (x)(b) is present and addresses rights issue matters → Answer "Yes"
    - If clause (x)(b) is missing → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (x)(b) is properly present, "No" if missing
    - Second line: "Clause (x)(b) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Rights Issue: [Conducted/None/Not Specified]"
    - Fourth line: "Proceeds Utilization: [Proper/Improper/Not Applicable]"
    - Fifth line: "Convertible Debentures: [Issued/None/Not Specified]"
    - Sixth line: "Clause Content: [Brief summary of what clause states]"
    - Seventh line: "Recommendation: [Professional guidance based on findings]"
  `,
  variables: []
},

// CARO Clause (xi)(a) - Fraud Check
caro_clause_xi_a_fraud: {
  key: 'caro_clause_xi_a_fraud',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (xi)(a) which relates to fraud by the company or on the company.

    **BACKGROUND ON CARO CLAUSE (xi)(a):**
    CARO clause (xi)(a) addresses whether any fraud by the company or any fraud on the company by its officers or employees has been noticed or reported during the year.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(xi)(a)" in the CARO document
    2. This clause typically addresses:
       - Fraud committed by the company
       - Fraud committed on the company by officers/employees
       - Whether fraud was noticed or reported during the year
       - Action taken regarding detected fraud

    3. Extract the complete sub-clause content
    4. Determine fraud status and reporting

    **EVALUATION CRITERIA:**
    - If clause (xi)(a) is present and addresses fraud matters → Answer "Yes"
    - If clause (xi)(a) is missing → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (xi)(a) is properly present, "No" if missing
    - Second line: "Clause (xi)(a) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Fraud by Company: [Noticed/None/Not Specified]"
    - Fourth line: "Fraud on Company: [Noticed/None/Not Specified]"
    - Fifth line: "Fraud Reporting: [Reported/Not Reported/Not Applicable]"
    - Sixth line: "Action Taken: [Described/Not Described/Not Applicable]"
    - Seventh line: "Recommendation: [Professional guidance based on findings]"
  `,
  variables: []
},

// CARO Clause (xi)(c) - Whistle-blower Check
caro_clause_xi_c_whistleblower: {
  key: 'caro_clause_xi_c_whistleblower',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (xi)(c) which relates to whistle-blower complaints.

    **BACKGROUND ON CARO CLAUSE (xi)(c):**
    CARO clause (xi)(c) addresses whether whistle-blower complaints received during the year, if any, have been dealt with by the management.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(xi)(c)" in the CARO document
    2. This clause typically addresses:
       - Whether whistle-blower complaints were received during the year
       - How complaints were dealt with by management
       - Status of complaint resolution
       - Whether proper mechanism exists for handling complaints

    3. Extract the complete sub-clause content
    4. Determine complaint handling status

    **EVALUATION CRITERIA:**
    - If clause (xi)(c) is present and addresses whistle-blower matters → Answer "Yes"
    - If clause (xi)(c) is missing → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (xi)(c) is properly present, "No" if missing
    - Second line: "Clause (xi)(c) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Whistle-blower Complaints: [Received/None/Not Specified]"
    - Fourth line: "Complaint Handling: [Proper/Inadequate/Not Applicable]"
    - Fifth line: "Resolution Status: [Resolved/Pending/Not Applicable]"
    - Sixth line: "Management Action: [Described/Not Described/Not Applicable]"
    - Seventh line: "Recommendation: [Professional guidance based on findings]"
  `,
  variables: []
},

// CARO Clause (vi) - Cost Records Check
caro_clause_vi_cost_records: {
  key: 'caro_clause_vi_cost_records',
  template: `
    Analyze this CARO Annexure PDF to check if it contains clause (vi) which relates to cost records and cost auditor.

    **BACKGROUND ON CARO CLAUSE (vi):**
    CARO clause (vi) addresses whether the Central Government has specified maintenance of cost records under section 148(1) of the Companies Act, 2013, for the products/services of the company, and whether the records have been made and maintained.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(vi)" in the CARO document
    2. This clause typically addresses:
       - Whether cost records are mandated for the company's products/services
       - Whether cost records have been made and maintained
       - Compliance with Section 148(1) of Companies Act, 2013
       - Turnover threshold (typically ₹35 crores or more)

    3. Extract the complete clause content
    4. Determine cost records maintenance status

    **EVALUATION CRITERIA:**
    - If clause (vi) is present and addresses cost records → Answer "Yes"
    - If clause (vi) is missing → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause (vi) is properly present, "No" if missing
    - Second line: "Clause (vi) Status: [Present and Complete/Present but Incomplete/Missing]"
    - Third line: "Cost Records Required: [Yes/No/Not Specified]"
    - Fourth line: "Cost Records Maintained: [Yes/No/Not Applicable]"
    - Fifth line: "Turnover Threshold: [Met/Not Met/Not Specified]"
    - Sixth line: "Section 148(1) Compliance: [Compliant/Non-Compliant/Not Applicable]"
    - Seventh line: "Recommendation: [Professional guidance based on findings]"
  `,
  variables: []
},



// Notes to Accounts Immovable Property Disputes Check
notes_immovable_property_disputes_check: {
  key: 'notes_immovable_property_disputes_check',
  template: `
    Analyze this Notes to Financial Statements PDF to check for any disclosure related to immovable property disputes.

    **SPECIFIC INSTRUCTIONS:**
    1. Look for notes or disclosures related to:
       - "Immovable property disputes"




    2. Look for specific phrases like:
       - "Disputes related to immovable property"


    **EVALUATION CRITERIA:**
    - If ANY mention of immovable property disputes found → Answer "Yes"
    - If no immovable property disputes mentioned → Answer "No"
    - If property mentioned but no disputes → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if immovable property disputes are disclosed, "No" if not disclosed
    - Second line: "Immovable Property Disputes: [Present/Not Present]"
    - Third line: "Details: [Brief description of disputes mentioned, or 'No disputes disclosed']"
    - Fourth line: "Location: [Which note/section contains the disclosure]"
    - Fifth line: "Nature of Disputes: [Brief summary of dispute types, or 'Not applicable']"

    **EXAMPLE RESPONSES:**

    **If Property Disputes Found:**
    Yes
    Immovable Property Disputes: Present
    Details: Company has pending litigation regarding ownership of land parcel worth ₹50 lakhs
    Location: Note 25 - Contingent Liabilities and Commitments
    Nature of Disputes: Land ownership dispute with local authority regarding property title

    **If No Property Disputes:**
    No
    Immovable Property Disputes: Not Present
    Details: No disputes disclosed regarding immovable properties
    Location: No specific disclosure found in contingent liabilities or legal proceedings notes
    Nature of Disputes: Not applicable

    **If Property Mentioned but No Disputes:**
    No
    Immovable Property Disputes: Not Present
    Details: Immovable properties mentioned but no disputes or litigation disclosed
    Location: Property details in fixed assets note but no dispute disclosure
    Nature of Disputes: Not applicable
  `,
  variables: []
},

// CARO Clause (i)(c) Immovable Property Title Issues Check
caro_clause_i_c_immovable_property: {
  key: 'caro_clause_i_c_immovable_property',
  template: `
    Analyze this CARO Annexure PDF to check clause (i)(c) for any issues related to immovable property title deeds or ownership problems.

    **BACKGROUND ON CARO CLAUSE (i)(c):**
    CARO clause (i)(c) addresses whether the title deeds of all immovable properties (other than leased properties) are held in the name of the company. This clause should highlight any title deed issues, ownership disputes, or property documentation problems.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(i)(c)" in the CARO document
    2. Look for indications of property title issues such as:
       - Title deeds not in company's name
       - Pending title deed transfers
       - Property ownership disputes
       - Missing or defective title documents
       - Legal proceedings affecting property titles
       - Encroachment or boundary issues
       - Properties under litigation

    3. Check if clause (i)(c) mentions:
       - "Title deeds are held in the name of the company" (clean status)
       - "Certain title deeds are not in company's name" (issues present)
       - "Title deed matters under litigation"
       - "Pending property transfers"
       - Any qualifications or exceptions regarding property titles

    **EVALUATION CRITERIA:**
    - If clause (i)(c) indicates title issues/disputes/problems → Answer "Yes"
    - If clause (i)(c) indicates clean title status with no issues → Answer "No"
    - If clause (i)(c) is missing altogether → Answer "No"

    Return STRICTLY in this format:
    - First line: "Yes" if clause indicates property title issues, "No" if clean or missing
    - Second line: "Clause (i)(c) Status: [Present with Issues/Present and Clean/Missing]"
    - Third line: "Title Deed Issues: [Present/None/Not Specified]"
    - Fourth line: "Property Problems: [Described/None/Not Applicable]"
    - Fifth line: "Ownership Status: [Issues Identified/Clean/Not Specified]"
    - Sixth line: "Clause Content Summary: [Brief summary of what clause states]"

    **EXAMPLE RESPONSES:**

    **If Property Title Issues Found:**
    Yes
    Clause (i)(c) Status: Present with Issues
    Title Deed Issues: Present
    Property Problems: Described
    Ownership Status: Issues Identified
    Clause Content Summary: Title deeds of certain immovable properties worth ₹X lakhs are under litigation and not yet transferred to company's name

    **If Clean Title Status:**
    No
    Clause (i)(c) Status: Present and Clean
    Title Deed Issues: None
    Property Problems: None
    Ownership Status: Clean
    Clause Content Summary: All title deeds of immovable properties are held in the name of the company with proper documentation

    **If Clause Missing:**
    No
    Clause (i)(c) Status: Missing
    Title Deed Issues: Not Specified
    Property Problems: Not Applicable
    Ownership Status: Not Specified
    Clause Content Summary: Clause (i)(c) not found in CARO annexure
  `,
  variables: []
},

//============================================
// bALANCE SHEET + NOTES OF Accounts + CARO
// ===========================================

bs_borrowings_note_numbers_extract: {
  key: 'bs_borrowings_note_numbers_extract',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Balance Sheet PDF to extract note numbers for borrowings:

    **TASK:**
    1. Look for 'Borrowings' under CURRENT LIABILITIES section
    2. Look for 'Borrowings' under NON-CURRENT LIABILITIES section
    3. Extract the Note Numbers mentioned against these borrowings (could be in parentheses, after "Note:", or as superscript)

    **SEARCH PATTERNS:**
    - "Borrowings (Note 15)" or "Borrowings - Note 15"
    - "Borrowings 15" or "Borrowings¹⁵"
    - Look for any number after "Borrowings" that indicates a note reference
    - Check for patterns like "15", "(15)", "Note 15", "Refer Note 15"

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 3 lines, nothing else:

    Current_Liabilities_Borrowings_Note: [Note Number or "Not Found"]
    Non_Current_Liabilities_Borrowings_Note: [Note Number or "Not Found"]
    Has_Borrowings: [Yes/No]

    **EXAMPLES:**
    If you see "Current Liabilities: Borrowings (Note 15)" and "Non-Current Liabilities: Borrowings (Note 14)":
    Current_Liabilities_Borrowings_Note: 15
    Non_Current_Liabilities_Borrowings_Note: 14
    Has_Borrowings: Yes

    If you see "Current Liabilities: Borrowings 8" and no non-current borrowings:
    Current_Liabilities_Borrowings_Note: 8
    Non_Current_Liabilities_Borrowings_Note: Not Found
    Has_Borrowings: Yes

    If no borrowings found:
    Current_Liabilities_Borrowings_Note: Not Found
    Non_Current_Liabilities_Borrowings_Note: Not Found
    Has_Borrowings: No
  `,
  variables: []
},


// Enhanced Notes Fixed Deposits analysis
notes_borrowings_fixed_deposits_analysis: {
  key: 'notes_borrowings_fixed_deposits_analysis',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF for Fixed Deposits in borrowings:

    **TASK:**
    Look for Note Numbers {currentLiabilitiesNote} and {nonCurrentLiabilitiesNote} in the Notes to Accounts.
    In these notes, check if "Fixed Deposits" or "Fixed Assets" are mentioned as part of borrowings.

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 4 lines, nothing else:

    Current_Note_Has_Fixed_Deposits: [Yes/No/Note Not Found]
    Non_Current_Note_Has_Fixed_Deposits: [Yes/No/Note Not Found]
    Fixed_Deposits_Details_Present: [Yes/No]
    Overall_Fixed_Deposits_In_Borrowings: [Yes/No]

    **EVALUATION RULES:**
    - If note number is found and mentions "Fixed Deposits" or "Fixed Assets" → "Yes"
    - If note number is found but no fixed deposits mentioned → "No"
    - If note number itself is not found → "Note Not Found"
    - Fixed_Deposits_Details_Present: "Yes" if either current or non-current has fixed deposits
    - Overall_Fixed_Deposits_In_Borrowings: "Yes" if any fixed deposits found in borrowings

    **EXAMPLE RESPONSE:**
    Current_Note_Has_Fixed_Deposits: Yes
    Non_Current_Note_Has_Fixed_Deposits: No
    Fixed_Deposits_Details_Present: Yes
    Overall_Fixed_Deposits_In_Borrowings: Yes
  `,
  variables: ['currentLiabilitiesNote', 'nonCurrentLiabilitiesNote']
},

// Enhanced CARO Clause V analysis
caro_clause_v_fixed_deposits_disclosure: {
  key: 'caro_clause_v_fixed_deposits_disclosure',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF for Clause (v) fixed deposits disclosure:

    **TASK:**
    Look for Clause (v) and determine the type of disclosure:

    **TYPE 1: STANDARD NOT APPLICABLE STATEMENT**
    Check if clause (v) contains this standard statement:
    "Based on our audit procedures & according to the information and explanation given to us, the Company has not accepted any depositor amounts which are deemed to be deposits within the meaning of the Act and the directives issued by the Reserve Bank of India and the provisions of sections 73 to 76 or any other relevant provisions of the Act and the rules framed thereunder. No order has been passed by Company Law Board or National Company Law Tribunal or Reserve Bank of India or any court or any other tribunal. Accordingly, paragraph 3(v) of the Order is not applicable to the Company."

    **TYPE 2: FIXED DEPOSITS DISCLOSURE**
    Check if clause (v) specifically discloses fixed deposits as deposits under the Act.

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 4 lines, nothing else:

    Clause_V_Present: [Yes/No]
    Clause_V_Type: [Standard_Not_Applicable/Fixed_Deposits_Disclosed/Other/Missing]
    Has_Standard_Statement: [Yes/No]
    Discloses_Fixed_Deposits: [Yes/No]

    **EVALUATION RULES:**
    - Clause_V_Present: "Yes" if clause (v) exists, "No" if missing
    - Clause_V_Type: "Standard_Not_Applicable" if contains standard statement, "Fixed_Deposits_Disclosed" if mentions fixed deposits, "Other" for different content, "Missing" if not found
    - Has_Standard_Statement: "Yes" if standard not applicable statement is present
    - Discloses_Fixed_Deposits: "Yes" if specifically mentions fixed deposits as deposits

    **EXAMPLE RESPONSE:**
    Clause_V_Present: Yes
    Clause_V_Type: Standard_Not_Applicable
    Has_Standard_Statement: Yes
    Discloses_Fixed_Deposits: No
  `,
  variables: []
},

//===========================================
// Balance Sheet Inventories extraction
// ===========================================

// Balance Sheet Inventories extraction
bs_inventories_note_extract: {
  key: 'bs_inventories_note_extract',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Balance Sheet PDF to extract note numbers for inventories:

    **TASK:**
    1. Look for 'Inventories' under CURRENT ASSETS section
    2. Extract the Note Number mentioned against inventories

    **SEARCH PATTERNS:**
    - "Inventories (Note 12)" or "Inventories - Note 12"
    - "Inventories 12" or "Inventories¹²"
    - "Stock (Note 12)" or "Stock 12"
    - Look for any number after "Inventories" or "Stock" that indicates a note reference
    - Check for patterns like "12", "(12)", "Note 12", "Refer Note 12"

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 2 lines, nothing else:

    Inventories_Note_Number: [Note Number or "Not Found"]
    Has_Inventories: [Yes/No]

    **EXAMPLES:**
    If you see "Current Assets: Inventories (Note 12)":
    Inventories_Note_Number: 12
    Has_Inventories: Yes

    If you see "Current Assets: Inventories 8":
    Inventories_Note_Number: 8
    Has_Inventories: Yes

    If no inventories found:
    Inventories_Note_Number: Not Found
    Has_Inventories: No
  `,
  variables: []
},

// Notes Goods in Transit analysis
notes_goods_in_transit_analysis: {
  key: 'notes_goods_in_transit_analysis',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF for Goods in Transit in inventories:

    **TASK:**
    Look for Note Number {inventoriesNote} in the Notes to Accounts.
    In this note, check if "Goods in Transit" is mentioned as part of inventories.

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 3 lines, nothing else:

    Note_Found: [Yes/No]
    Goods_In_Transit_Present: [Yes/No]
    Goods_In_Transit_Details: [Brief description or "Not Found"]

    **EVALUATION RULES:**
    - Note_Found: "Yes" if note number exists, "No" if not found
    - Goods_In_Transit_Present: "Yes" if "Goods in Transit" mentioned, "No" if not mentioned
    - Goods_In_Transit_Details: Brief description of goods in transit if found, "Not Found" if not mentioned

    **EXAMPLE RESPONSE:**
    Note_Found: Yes
    Goods_In_Transit_Present: Yes
    Goods_In_Transit_Details: Goods in transit worth ₹50 lakhs
  `,
  variables: ['inventoriesNote']
},

// Notes Inventory Write-off analysis
notes_inventory_writeoff_analysis: {
  key: 'notes_inventory_writeoff_analysis',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF for Inventory Write-offs:

    **TASK:**
    Look for Note Number {inventoriesNote} in the Notes to Accounts.
    In this note, check if "Write off of inventory" or "Inventory write-off" is mentioned.

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 3 lines, nothing else:

    Note_Found: [Yes/No]
    Inventory_Writeoff_Present: [Yes/No]
    Writeoff_Details: [Brief description or "Not Found"]

    **EVALUATION RULES:**
    - Note_Found: "Yes" if note number exists, "No" if not found
    - Inventory_Writeoff_Present: "Yes" if write-off mentioned, "No" if not mentioned
    - Writeoff_Details: Brief description of write-off if found, "Not Found" if not mentioned

    **EXAMPLE RESPONSE:**
    Note_Found: Yes
    Inventory_Writeoff_Present: Yes
    Writeoff_Details: Inventory write-off of ₹25 lakhs during the year
  `,
  variables: ['inventoriesNote']
},

// // Notes Secured Borrowings analysis
// notes_secured_borrowings_analysis: {
//   key: 'notes_secured_borrowings_analysis',
//   template: `
//     🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

//     Analyze this Notes to Accounts PDF for Secured Borrowings amount:

//     **TASK:**
//     Look for Note Number {currentBorrowingsNote} in the Notes to Accounts.
//     In this note, check if "Secured Borrowings" is mentioned and extract the amount.

//     **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
//     Return ONLY these 4 lines, nothing else:

//     Note_Found: [Yes/No]
//     Secured_Borrowings_Present: [Yes/No]
//     Secured_Borrowings_Amount: [Amount in crores or "Not Found"]
//     Amount_More_Than_5_Crores: [Yes/No]

//     **EVALUATION RULES:**
//     - Note_Found: "Yes" if note number exists, "No" if not found
//     - Secured_Borrowings_Present: "Yes" if secured borrowings mentioned, "No" if not mentioned
//     - Secured_Borrowings_Amount: Extract amount and convert to crores, "Not Found" if not mentioned
//     - Amount_More_Than_5_Crores: "Yes" if amount > 5 crores, "No" if ≤ 5 crores

//     **EXAMPLE RESPONSE:**
//     Note_Found: Yes
//     Secured_Borrowings_Present: Yes
//     Secured_Borrowings_Amount: 15.5
//     Amount_More_Than_5_Crores: Yes
//   `,
//   variables: ['currentBorrowingsNote']
// },

// CARO Clause ii(a) Goods in Transit analysis
caro_clause_ii_a_goods_in_transit: {
  key: 'caro_clause_ii_a_goods_in_transit',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to check if clause (ii)(a) properly addresses goods in transit exclusion from physical verification.

    **BACKGROUND ON CARO CLAUSE (ii)(a) FOR GOODS IN TRANSIT:**
    When a company has goods in transit in their inventory, CARO clause (ii)(a) MUST specifically mention that physical verification excludes "goods in transit" or "stocks lying with third parties" to comply with inventory verification standards.

    **SPECIFIC INSTRUCTIONS:**
    1. Search specifically for clause "(ii)(a)" or "(ii) (a)" in the CARO document
    2. Check if the clause mentions ANY of these exclusion phrases:
       - "goods in transit"
       - "goods in-transit"
       - "stock in transit"
       - "stocks lying with third parties"
       - "goods lying with third parties"
       - "excluding goods in transit"
       - "except goods in transit"
       - "other than goods in transit"

    3. The clause should indicate that physical verification was conducted BUT excludes goods in transit
    4. This exclusion is MANDATORY when goods in transit exist in inventory

    **EVALUATION CRITERIA:**
    - If clause (ii)(a) is present AND mentions goods in transit exclusion → Answer "Yes"
    - If clause (ii)(a) is present but does NOT mention goods in transit exclusion → Answer "No"
    - If clause (ii)(a) is missing entirely → Answer "No"

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 6 lines, nothing else:

    Clause_II_A_Present: [Yes/No]
    Mentions_Goods_In_Transit_Exclusion: [Yes/No]
    Exclusion_Phrases_Found: [List specific phrases found or "None"]
    Physical_Verification_Conducted: [Yes/No]
    Has_Proper_Goods_In_Transit_Disclosure: [Yes/No]
    Clause_Content_Summary: [Brief summary of what clause states about verification scope]

    **EVALUATION RULES:**
    - Clause_II_A_Present: "Yes" if clause (ii)(a) exists in CARO
    - Mentions_Goods_In_Transit_Exclusion: "Yes" if any exclusion phrases found
    - Exclusion_Phrases_Found: List exact phrases found (e.g., "excluding goods in transit")
    - Physical_Verification_Conducted: "Yes" if clause mentions verification was done
    - Has_Proper_Goods_In_Transit_Disclosure: "Yes" if both clause exists AND mentions exclusion
    - Clause_Content_Summary: Brief description of verification scope mentioned

    **EXAMPLE RESPONSES:**

    **If Clause (ii)(a) Present WITH Goods in Transit Exclusion:**
    Clause_II_A_Present: Yes
    Mentions_Goods_In_Transit_Exclusion: Yes
    Exclusion_Phrases_Found: "excluding goods in transit and stocks lying with third parties"
    Physical_Verification_Conducted: Yes
    Has_Proper_Goods_In_Transit_Disclosure: Yes
    Clause_Content_Summary: Physical verification conducted excluding goods in transit and third party stocks

    **If Clause (ii)(a) Present but NO Goods in Transit Exclusion:**
    Clause_II_A_Present: Yes
    Mentions_Goods_In_Transit_Exclusion: No
    Exclusion_Phrases_Found: None
    Physical_Verification_Conducted: Yes
    Has_Proper_Goods_In_Transit_Disclosure: No
    Clause_Content_Summary: Physical verification conducted but no mention of goods in transit exclusion

    **If Clause (ii)(a) Missing:**
    Clause_II_A_Present: No
    Mentions_Goods_In_Transit_Exclusion: No
    Exclusion_Phrases_Found: None
    Physical_Verification_Conducted: No
    Has_Proper_Goods_In_Transit_Disclosure: No
    Clause_Content_Summary: Clause (ii)(a) not found in CARO document
  `,
  variables: []
},

// CARO Clause ii(a) Inventory Write-off analysis
caro_clause_ii_a_inventory_writeoff: {
  key: 'caro_clause_ii_a_inventory_writeoff',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF for Clause (ii)(a) inventory verification and discrepancies:

    **TASK:**
    Look for Clause (ii)(a) and check if it mentions verification coverage, procedures, and discrepancies.

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 5 lines, nothing else:

    Clause_II_A_Present: [Yes/No]
    Mentions_Verification_Coverage: [Yes/No]
    Mentions_Discrepancies: [Yes/No]
    Mentions_10_Percent_Rule: [Yes/No]
    Has_Proper_Writeoff_Disclosure: [Yes/No]

    **EVALUATION RULES:**
    - Clause_II_A_Present: "Yes" if clause (ii)(a) exists
    - Mentions_Verification_Coverage: "Yes" if coverage and procedure mentioned
    - Mentions_Discrepancies: "Yes" if discrepancies between physical and book records mentioned
    - Mentions_10_Percent_Rule: "Yes" if "10% or more" discrepancy rule mentioned
    - Has_Proper_Writeoff_Disclosure: "Yes" if coverage, discrepancies, and 10% rule all mentioned

    **EXAMPLE RESPONSE:**
    Clause_II_A_Present: Yes
    Mentions_Verification_Coverage: Yes
    Mentions_Discrepancies: Yes
    Mentions_10_Percent_Rule: Yes
    Has_Proper_Writeoff_Disclosure: Yes
  `,
  variables: []
},

// CARO Clause ii(b) Quarterly Returns analysis
caro_clause_ii_b_quarterly_returns: {
  key: 'caro_clause_ii_b_quarterly_returns',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF for Clause (ii)(b) quarterly returns submission:

    **TASK:**
    Look for Clause (ii)(b) and check if it mentions quarterly returns to banks/financial institutions.

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 4 lines, nothing else:

    Clause_II_B_Present: [Yes/No]
    Mentions_Quarterly_Returns: [Yes/No]
    Mentions_Banks_Financial_Institutions: [Yes/No]
    Has_Proper_Quarterly_Returns_Disclosure: [Yes/No]

    **EVALUATION RULES:**
    - Clause_II_B_Present: "Yes" if clause (ii)(b) exists
    - Mentions_Quarterly_Returns: "Yes" if "quarterly returns" mentioned
    - Mentions_Banks_Financial_Institutions: "Yes" if "banks" or "financial institutions" mentioned
    - Has_Proper_Quarterly_Returns_Disclosure: "Yes" if both quarterly returns and banks/financial institutions mentioned

    **EXAMPLE RESPONSE:**
    Clause_II_B_Present: Yes
    Mentions_Quarterly_Returns: Yes
    Mentions_Banks_Financial_Institutions: Yes
    Has_Proper_Quarterly_Returns_Disclosure: Yes
  `,
  variables: []
},

//============================================
// Extract loans from Notes to Accounts - Related Party Transactions
// ===========================================



notes_related_party_loans_extract: {
  key: 'notes_related_party_loans_extract',
  template: `
    🔍 STEP-BY-STEP SEARCH for Related Party note:

    **STEP 1: FIND THE NOTE**
    Look for these exact patterns:
    1. "Related party and transactions"
    2. "Related Party and Transactions"
    3. "Related party transactions"
    4. "Related Party Transactions"
    5. Any note number followed by "Related party" (like "37. Related party")

    **STEP 2: EXTRACT LOAN INFORMATION**
    Once you find the note, look for:
    - Tables showing different types of transactions
    - Rows labeled: "Loan given", "Loans given", "Advance given", "Loan to"
    - Amounts in columns for different entities (Subsidiaries, Associates, etc.)

    **YOU MUST RETURN EXACTLY THIS FORMAT (copy these lines exactly):**

    Related_Party_Note_Found: Yes
    Note_Number_Confirmed: 37
    Note_Heading_Found: 37. Related party and transactions
    Search_Patterns_Tried: Related party and transactions, Related Party Transactions
    Headings_Found_Containing_Related: 37. Related party and transactions
    Loan_Given_Amount: 72.17
    Currency_Unit: Crores
    Has_Loan_Given: Yes
    Loan_Given_Details: Loans to Subsidiaries 72.17 Crores
    Debug_Info: Found note 37 with related party transactions table

    **CRITICAL INSTRUCTIONS:**
    - If you find "37. Related party and transactions" → set Related_Party_Note_Found: Yes
    - Look inside that section for loan/advance amounts
    - Extract the total amount of loans given
    - If no loans found, set Loan_Given_Amount: 0 and Has_Loan_Given: No
    - Always fill in the Debug_Info with what you actually found

    **EXAMPLE if you find the note but no loans:**
    Related_Party_Note_Found: Yes
    Note_Number_Confirmed: 37
    Note_Heading_Found: 37. Related party and transactions
    Search_Patterns_Tried: Related party and transactions
    Headings_Found_Containing_Related: 37. Related party and transactions
    Loan_Given_Amount: 0
    Currency_Unit: Crores
    Has_Loan_Given: No
    Loan_Given_Details: No loans given to related parties
    Debug_Info: Found note 37 but no loan transactions in the table

    **EXAMPLE if you cannot find the note:**
    Related_Party_Note_Found: No
    Note_Number_Confirmed: Not Found
    Note_Heading_Found: Not Found
    Search_Patterns_Tried: Related party and transactions, Related Party Transactions, Related party
    Headings_Found_Containing_Related: None found
    Loan_Given_Amount: 0
    Currency_Unit: Crores
    Has_Loan_Given: No
    Loan_Given_Details: No related party note found
    Debug_Info: Searched for related party patterns but could not locate the note
  `,
  variables: []
},
// Extract CARO Clause (iii)(a)(A) amounts
caro_clause_iii_a_A_loans_extract: {
  key: 'caro_clause_iii_a_A_loans_extract',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to extract loan amounts from clause (iii)(a)(A).

    **TASK:** Find EXACTLY clause "(iii)(a)(A)" and extract all loan-related amounts.

    **WHAT TO LOOK FOR IN CLAUSE (iii)(a)(A):**
    1. **Loans given to subsidiaries, joint ventures, associates**
    2. **Advances in the nature of loans**
    3. **Financial guarantees**
    4. **Securities provided**
    5. **Total aggregate amounts during the year**
    6. **Outstanding balances**

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Clause_III_A_A_Found: [Yes/No]
    Total_Loans_Amount: [Amount in lakhs/crores or 0]
    Loans_During_Year: [Amount or 0]
    Outstanding_Balance: [Amount or 0]
    Guarantees_Amount: [Amount or 0]
    Securities_Amount: [Amount or 0]
    Currency_Unit: [Lakhs/Crores/Rupees]
    Has_Loan_Disclosures: [Yes/No]
    Clause_Content_Summary: [Brief summary of what clause states]

    **EVALUATION RULES:**
    - If clause (iii)(a)(A) is not found → Clause_III_A_A_Found: No
    - Extract all monetary amounts mentioned in the clause
    - Convert all amounts to same unit for comparison
    - Focus on aggregate amounts and outstanding balances

    **EXAMPLE RESPONSE:**
    Clause_III_A_A_Found: Yes
    Total_Loans_Amount: 250.50
    Loans_During_Year: 50.00
    Outstanding_Balance: 250.50
    Guarantees_Amount: 0
    Securities_Amount: 0
    Currency_Unit: Lakhs
    Has_Loan_Disclosures: Yes
    Clause_Content_Summary: Company has given loans aggregating Rs. 250.50 lakhs to subsidiaries and associates
  `,
  variables: []
},

// Extract CARO Clause (iii)(f) amounts
caro_clause_iii_f_loans_extract: {
  key: 'caro_clause_iii_f_loans_extract',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to extract loan amounts from clause (iii)(f).

    **TASK:** Find EXACTLY clause "(iii)(f)" and extract all loan-related amounts.

    **WHAT TO LOOK FOR IN CLAUSE (iii)(f):**
    1. **Loans to related parties as per section 177 and 188**
    2. **Compliance with related party transaction requirements**
    3. **Aggregate amounts of related party loans**
    4. **Outstanding balances with related parties**

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Clause_III_F_Found: [Yes/No]
    Related_Party_Loans_Amount: [Amount in lakhs/crores or 0]
    Outstanding_RP_Balance: [Amount or 0]
    Section_177_188_Compliance: [Yes/No/Not Mentioned]
    Currency_Unit: [Lakhs/Crores/Rupees]
    Has_RP_Loan_Disclosures: [Yes/No]
    Clause_Content_Summary: [Brief summary of what clause states]

    **EVALUATION RULES:**
    - If clause (iii)(f) is not found → Clause_III_F_Found: No
    - Extract monetary amounts related to related party loans
    - Check if clause mentions section 177 and 188 compliance
    - Focus on related party lending specifically

    **EXAMPLE RESPONSE:**
    Clause_III_F_Found: Yes
    Related_Party_Loans_Amount: 250.50
    Outstanding_RP_Balance: 250.50
    Section_177_188_Compliance: Yes
    Currency_Unit: Lakhs
    Has_RP_Loan_Disclosures: Yes
    Clause_Content_Summary: Related party loans of Rs. 250.50 lakhs comply with sections 177 and 188
  `,
  variables: []
},
// Point 1: Notes New Investments/Loans vs CARO Clause (iii)
notes_new_investments_loans_extract: {
  key: 'notes_new_investments_loans_extract',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF to extract information about NEW investments, loans, and guarantees during the current year:

    **TASK:**
    Check these specific areas:
    1. **Investments note**: Any NEW investments in current year (other than Mutual funds)
    2. **Loans note** (current or non-current assets): Any NEW loans given to any party in current year
    3. **Contingent liabilities note**: Any NEW financial guarantees given or securities provided during the year

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    New_Investments_Present: [Yes/No]
    New_Investments_Amount: [Amount in lakhs/crores or 0]
    New_Loans_Present: [Yes/No]
    New_Loans_Amount: [Amount in lakhs/crores or 0]
    New_Guarantees_Present: [Yes/No]
    New_Guarantees_Amount: [Amount in lakhs/crores or 0]
    Total_New_Values: [Sum of all new amounts]
    Currency_Unit: [Lakhs/Crores/Rupees]
    Has_New_Activities: [Yes if any new investments/loans/guarantees, No if none]
    Details_Summary: [Brief description of what new activities were found]

    **EVALUATION RULES:**
    - Only count NEW investments/loans/guarantees in CURRENT YEAR
    - Exclude mutual fund investments
    - Include all forms of loans (advances in nature of loans, etc.)
    - Include financial guarantees and securities provided
    - Sum up all amounts for total

    **EXAMPLE RESPONSE:**
    New_Investments_Present: Yes
    New_Investments_Amount: 50.00
    New_Loans_Present: Yes
    New_Loans_Amount: 25.00
    New_Guarantees_Present: No
    New_Guarantees_Amount: 0
    Total_New_Values: 75.00
    Currency_Unit: Lakhs
    Has_New_Activities: Yes
    Details_Summary: New investments in subsidiary ₹50L, new loan to associate ₹25L
  `,
  variables: []
},

// Point 1: CARO Clause (iii) New Activities Check
caro_clause_iii_new_activities: {
  key: 'caro_clause_iii_new_activities',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to check if clause (iii) addresses new investments, loans, and advances:

    **TASK:**
    Look for clause (iii) and check if it covers:
    - New investments made during the year
    - New loans and advances given
    - Terms and conditions of investments/loans
    - Whether activities are prejudicial to company's interest

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Clause_III_Present: [Yes/No]
    Addresses_New_Investments: [Yes/No]
    Addresses_New_Loans: [Yes/No]
    Mentions_Terms_Conditions: [Yes/No]
    Prejudicial_Assessment: [Present/Absent]
    Has_Adequate_Coverage: [Yes/No]
    Clause_Content_Summary: [Brief summary of what clause states]

    **EXAMPLE RESPONSE:**
    Clause_III_Present: Yes
    Addresses_New_Investments: Yes
    Addresses_New_Loans: Yes
    Mentions_Terms_Conditions: Yes
    Prejudicial_Assessment: Present
    Has_Adequate_Coverage: Yes
    Clause_Content_Summary: Clause (iii) states investments and loans are not prejudicial to company's interest
  `,
  variables: []
},

// Point 2: Notes Provision for Doubtful Loans Check
notes_provision_doubtful_loans: {
  key: 'notes_provision_doubtful_loans',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF to check for provisions for doubtful loans and advances:

    **TASK:**
    Look for:
    - Provision for doubtful loans
    - Provision for doubtful advances
    - Bad debts provision
    - Impairment of loans/advances

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Provision_For_Doubtful_Loans_Present: [Yes/No]
    Provision_Amount: [Amount in lakhs/crores or 0]
    Provision_Details: [Brief description or "Not Found"]
    Currency_Unit: [Lakhs/Crores/Rupees]
    Location_In_Notes: [Which note contains the provision]

    **EXAMPLE RESPONSE:**
    Provision_For_Doubtful_Loans_Present: Yes
    Provision_Amount: 15.00
    Provision_Details: Provision for doubtful trade receivables and advances
    Currency_Unit: Lakhs
    Location_In_Notes: Note 12 - Trade Receivables
  `,
  variables: []
},

// Point 2: CARO Clause (iii)(b)(c)(d)(e) Doubtful Loans Check
caro_clause_iii_doubtful_loans: {
  key: 'caro_clause_iii_doubtful_loans',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to check if clause (iii) sub-clauses (b), (c), (d), (e) are considered:

    **TASK:**
    Look for clause (iii) and specifically check for sub-clauses:
    - (b) Recovery of loans and advances
    - (c) Terms and conditions of loans
    - (d) Schedule of repayment
    - (e) Overdue amounts

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Clause_III_B_Present: [Yes/No]
    Clause_III_C_Present: [Yes/No]
    Clause_III_D_Present: [Yes/No]
    Clause_III_E_Present: [Yes/No]
    All_Sub_Clauses_Present: [Yes/No]
    Clause_Content_Summary: [Brief summary including mention of prejudicial assessment]

    **EXAMPLE RESPONSE:**
    Clause_III_B_Present: Yes
    Clause_III_C_Present: Yes
    Clause_III_D_Present: Yes
    Clause_III_E_Present: Yes
    All_Sub_Clauses_Present: Yes
    Clause_Content_Summary: All sub-clauses present, states investments and loans are not prejudicial to company's interest
  `,
  variables: []
},

// Point 3: Notes Aggregate Activities vs Capital Check
notes_aggregate_activities_capital: {
  key: 'notes_aggregate_activities_capital',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF to calculate aggregate of new activities vs capital:

    **TASK:**
    1. Sum up ALL: new loans + advances in nature of loans + investments + guarantees + securities
    2. From multiple notes: loans, investments, contingent liabilities, related parties
    3. Get paid-up share capital + reserves and surplus (minus revaluation reserve) at beginning of year
    4. Calculate if aggregate > 60% of capital base

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Total_Loans_Advances: [Amount]
    Total_Investments: [Amount]
    Total_Guarantees_Securities: [Amount]
    Aggregate_Amount: [Sum of above three]
    Paid_Up_Capital: [Amount at beginning of year]
    Reserves_Surplus: [Amount minus revaluation reserve]
    Capital_Base: [Paid up capital + reserves surplus]
    Percentage_Of_Capital: [Aggregate as % of capital base]
    Exceeds_60_Percent: [Yes if >60%, No if ≤60%]
    Currency_Unit: [Lakhs/Crores/Rupees]

    **EXAMPLE RESPONSE:**
    Total_Loans_Advances: 100.00
    Total_Investments: 150.00
    Total_Guarantees_Securities: 50.00
    Aggregate_Amount: 300.00
    Paid_Up_Capital: 200.00
    Reserves_Surplus: 180.00
    Capital_Base: 380.00
    Percentage_Of_Capital: 78.95
    Exceeds_60_Percent: Yes
    Currency_Unit: Lakhs
  `,
  variables: []
},

// Point 3: CARO Clause (iv) Sections 185-186 Check
caro_clause_iv_sections_185_186: {
  key: 'caro_clause_iv_sections_185_186',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to check clause (iv) for Sections 185 and 186 compliance:

    **TASK:**
    Look for clause (iv) and check if it mentions:
    - Section 185 compliance (loans to directors)
    - Section 186 compliance (investments and loans)
    - Grant of loans and making investments

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Clause_IV_Present: [Yes/No]
    Mentions_Section_185: [Yes/No]
    Mentions_Section_186: [Yes/No]
    Mentions_Loans_Investments: [Yes/No]
    Compliance_Statement: [Present/Absent]
    Has_Adequate_Coverage: [Yes/No]
    Clause_Content_Summary: [Brief summary of clause content]

    **EXAMPLE RESPONSE:**
    Clause_IV_Present: Yes
    Mentions_Section_185: Yes
    Mentions_Section_186: Yes
    Mentions_Loans_Investments: Yes
    Compliance_Statement: Present
    Has_Adequate_Coverage: Yes
    Clause_Content_Summary: Company has complied with provisions of Sections 185 and 186 for loans and investments
  `,
  variables: []
},

// Point 4: Notes Statutory Dues Comparison Check
notes_statutory_dues_comparison: {
  key: 'notes_statutory_dues_comparison',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF to compare statutory dues between current and previous year:

    **TASK:**
    1. Find "Other Current Liabilities" or "Provisions" notes
    2. Compare statutory dues amounts between current year and previous year
    3. Check if any amounts remain exactly the same (indicating non-payment)

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Statutory_Dues_Note_Found: [Yes/No]
    Current_Year_Amount: [Amount]
    Previous_Year_Amount: [Amount]
    Amounts_Same: [Yes if exactly same, No if different]
    Dues_Types_Found: [List of statutory dues types found]
    Static_Dues_Present: [Yes if same amounts indicating non-payment, No if changing]
    Currency_Unit: [Lakhs/Crores/Rupees]
    Note_Location: [Which note contains statutory dues]

    **EXAMPLE RESPONSE:**
    Statutory_Dues_Note_Found: Yes
    Current_Year_Amount: 25.50
    Previous_Year_Amount: 25.50
    Amounts_Same: Yes
    Dues_Types_Found: TDS payable, PF payable, ESI payable
    Static_Dues_Present: Yes
    Currency_Unit: Lakhs
    Note_Location: Note 18 - Other Current Liabilities
  `,
  variables: []
},

// Point 4: CARO Clause (vii)(a) Statutory Dues Check
caro_clause_vii_a_statutory_dues_new: {
  key: 'caro_clause_vii_a_statutory_dues_new',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to check clause (vii)(a) for statutory dues compliance:

    **TASK:**
    Look for clause (vii)(a) and check if it mentions:
    - Regular deposit of undisputed statutory dues
    - GST, PF, ESI, Income Tax, Customs, Excise, Cess
    - No arrears for more than 6 months

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Clause_VII_A_Present: [Yes/No]
    Mentions_Regular_Deposit: [Yes/No]
    Lists_Statutory_Dues_Types: [Yes/No]
    Mentions_Six_Months_Rule: [Yes/No]
    States_No_Arrears: [Yes/No]
    Has_Adequate_Coverage: [Yes/No]
    Clause_Content_Summary: [Brief summary of clause content]

    **EXAMPLE RESPONSE:**
    Clause_VII_A_Present: Yes
    Mentions_Regular_Deposit: Yes
    Lists_Statutory_Dues_Types: Yes
    Mentions_Six_Months_Rule: Yes
    States_No_Arrears: Yes
    Has_Adequate_Coverage: Yes
    Clause_Content_Summary: Company regular in depositing statutory dues, no arrears over 6 months
  `,
  variables: []
},

// Point 5: Notes Share Capital/Debt Increase Check
notes_capital_debt_increase: {
  key: 'notes_capital_debt_increase',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF to check for increases in share capital or debt instruments:

    **TASK:**
    1. Check "Share Capital" note for any increase during the year
    2. Check for any new debentures or debt instruments issued
    3. Look for rights issue, bonus issue, or new debt issuance

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Share_Capital_Increase: [Yes/No]
    Share_Capital_Increase_Amount: [Amount or 0]
    Debt_Instruments_Issued: [Yes/No]
    Debt_Instruments_Amount: [Amount or 0]
    Rights_Issue_Present: [Yes/No]
    Debentures_Issued: [Yes/No]
    Any_Capital_Raising: [Yes if any increase, No if none]
    Currency_Unit: [Lakhs/Crores/Rupees]
    Details_Summary: [Brief description of capital raising activities]

    **EXAMPLE RESPONSE:**
    Share_Capital_Increase: Yes
    Share_Capital_Increase_Amount: 100.00
    Debt_Instruments_Issued: No
    Debt_Instruments_Amount: 0
    Rights_Issue_Present: Yes
    Debentures_Issued: No
    Any_Capital_Raising: Yes
    Currency_Unit: Lakhs
    Details_Summary: Rights issue of ₹100 lakhs increasing share capital
  `,
  variables: []
},

// Point 5: CARO Clause (x)(a) Capital Raising Check
caro_clause_x_a_capital_raising: {
  key: 'caro_clause_x_a_capital_raising',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to check clause (x)(a) for capital raising activities:

    **TASK:**
    Look for clause (x)(a) and check if it mentions:
    - Money raised by way of initial public offering or further public offering
    - Rights issue or preferential allotment
    - Proper utilization of raised funds

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Clause_X_A_Present: [Yes/No]
    Mentions_Public_Offering: [Yes/No]
    Mentions_Rights_Issue: [Yes/No]
    Mentions_Fund_Utilization: [Yes/No]
    Addresses_Capital_Raising: [Yes/No]
    Has_Adequate_Coverage: [Yes/No]
    Clause_Content_Summary: [Brief summary of clause content]

    **EXAMPLE RESPONSE:**
    Clause_X_A_Present: Yes
    Mentions_Public_Offering: No
    Mentions_Rights_Issue: Yes
    Mentions_Fund_Utilization: Yes
    Addresses_Capital_Raising: Yes
    Has_Adequate_Coverage: Yes
    Clause_Content_Summary: Funds raised through rights issue utilized for intended purposes
  `,
  variables: []
},

// Point 6: Notes Related Party Transactions >10% Turnover Check
notes_rpt_above_10_percent_turnover: {
  key: 'notes_rpt_above_10_percent_turnover',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF to check if any related party transactions exceed 10% of turnover:

    **TASK:**
    1. Find related party transactions note
    2. Find revenue/turnover amount from P&L or notes
    3. Calculate if any single party transactions > 10% of turnover

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    RPT_Note_Found: [Yes/No]
    Company_Turnover: [Amount]
    Ten_Percent_Threshold: [10% of turnover]
    Highest_RPT_Party: [Name of party with highest transactions]
    Highest_RPT_Amount: [Amount of highest RPT]
    Exceeds_10_Percent: [Yes if any party >10% turnover, No if all ≤10%]
    Currency_Unit: [Lakhs/Crores/Rupees]
    Details_Summary: [Brief description of major RPTs]

    **EXAMPLE RESPONSE:**
    RPT_Note_Found: Yes
    Company_Turnover: 1000.00
    Ten_Percent_Threshold: 100.00
    Highest_RPT_Party: ABC Subsidiary Ltd
    Highest_RPT_Amount: 150.00
    Exceeds_10_Percent: Yes
    Currency_Unit: Lakhs
    Details_Summary: Transactions with ABC Subsidiary ₹150L exceeds 10% threshold
  `,
  variables: []
},

// Point 6: CARO Clause (xiii) Minority Approval Check
caro_clause_xiii_minority_approval: {
  key: 'caro_clause_xiii_minority_approval',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF to check clause (xiii) for minority approval of major related party transactions:

    **TASK:**
    Look for clause (xiii) and check if it mentions:
    - Related party transactions compliance
    - Majority of minority approval
    - Section 177 and 188 compliance
    - Board/shareholders approval process

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Clause_XIII_Present: [Yes/No]
    Mentions_RPT_Compliance: [Yes/No]
    Mentions_Minority_Approval: [Yes/No]
    Mentions_Section_177_188: [Yes/No]
    Addresses_Approval_Process: [Yes/No]
    Has_Adequate_Coverage: [Yes/No]
    Clause_Content_Summary: [Brief summary of clause content]

    **EXAMPLE RESPONSE:**
    Clause_XIII_Present: Yes
    Mentions_RPT_Compliance: Yes
    Mentions_Minority_Approval: Yes
    Mentions_Section_177_188: Yes
    Addresses_Approval_Process: Yes
    Has_Adequate_Coverage: Yes
    Clause_Content_Summary: Related party transactions comply with Sections 177-188, minority approval documented
  `,
  variables: []
},

// ===========================================
// ENHANCED AUDIT REPORT CROSS-REFERENCE PROMPTS
// ===========================================

// ===========================================
// ROBUST AUDIT REPORT CROSS-REFERENCE PROMPTS
// ===========================================

// Audit Report CARO Reference Extraction - ROBUST
audit_report_caro_reference_extract: {
  key: 'audit_report_caro_reference_extract',
  template: `
    🔍 CRITICAL: You MUST analyze the ENTIRE document and return EXACTLY the format shown below.

    **YOUR TASK:**
    Find the paragraph that mentions BOTH "Companies (Auditors' Report) Order" AND "Annexure A"

    **STEP-BY-STEP SEARCH:**
    1. First, find the section "Report on Other Legal and Regulatory Requirements"
    2. Then look through ALL numbered paragraphs (1., 2., 3., etc.) in that section
    3. For EACH paragraph, check if it contains BOTH:
       - "Companies (Auditors' Report) Order" (may include year like 2020)
       - "Annexure A" (with or without quotes)
    4. When you find a paragraph with BOTH phrases, extract that paragraph number

    **EXAMPLE TEXT YOU SHOULD FIND AND MATCH:**
    "1. As required by the Companies (Auditors' Report) Order, 2020 ("the Order"), issued by the Central Government of India in terms of sub-section (11) of section 143 of the Act, we give in the "Annexure A" a statement on the matter specified in paragraphs 3 (xxi) and 4 of the Order, to the extent applicable."

    **DEBUG YOUR SEARCH:**
    - Search for "Companies (Auditors' Report) Order" - if found, note which paragraph
    - Search for "Annexure A" - if found, note which paragraph
    - Check if they appear in the SAME paragraph

    **CRITICAL OUTPUT FORMAT - RETURN EXACTLY THESE LINES:**

    CARO_Reference_Found: [Yes/No]
    CARO_Paragraph_Number: [Number like "1" or "Not Found"]
    Full_Reference_Text: [Complete text of the paragraph containing both phrases]
    Section_Found: [Yes/No]
    Contains_CARO_Order: [Yes/No]
    Contains_Annexure_A: [Yes/No]
    Debug_CARO_Location: [Which paragraph contains "Companies (Auditors' Report) Order"]
    Debug_Annexure_A_Location: [Which paragraph contains "Annexure A"]

    **SEARCH REQUIREMENTS:**
    - Be case-insensitive in your search
    - Look for "Companies (Auditors' Report) Order" with any year (2020, 2016, etc.)
    - Look for "Annexure A" with or without quotes
    - The section MUST be called "Report on Other Legal and Regulatory Requirements"

    **EXAMPLE SUCCESSFUL RESPONSE:**
    CARO_Reference_Found: Yes
    CARO_Paragraph_Number: 1
    Full_Reference_Text: As required by the Companies (Auditors' Report) Order, 2020 ("the Order"), issued by the Central Government of India in terms of sub-section (11) of section 143 of the Act, we give in the "Annexure A" a statement on the matter specified in paragraphs 3 (xxi) and 4 of the Order, to the extent applicable.
    Section_Found: Yes
    Contains_CARO_Order: Yes
    Contains_Annexure_A: Yes
    Debug_CARO_Location: Paragraph 1
    Debug_Annexure_A_Location: Paragraph 1
  `,
  variables: []
},

// Audit Report Annexure B Reference Extraction - ROBUST
audit_report_annexure_b_reference_extract: {
  key: 'audit_report_annexure_b_reference_extract',
  template: `
    🔍 CRITICAL: You MUST analyze the ENTIRE document and return EXACTLY the format shown below.

    **YOUR TASK:**
    Find the paragraph that mentions "Annexure B" (with or without quotes)

    **STEP-BY-STEP SEARCH:**
    1. First, find the section "Report on Other Legal and Regulatory Requirements"
    2. Look through ALL numbered paragraphs AND sub-paragraphs (1., 2., 2a), 2b), 2c), 2d), 2e), 2f), etc.)
    3. Find ANY paragraph that contains "Annexure B"
    4. Extract the COMPLETE paragraph reference including sub-letters

    **EXAMPLE TEXT YOU SHOULD FIND AND MATCH:**
    "f) With respect to adequacy of the internal financial controls over financial statements of the Holding Company and its subsidiary companies which are incorporated in India and the operating effectiveness of such controls, refer to our separate Report in "Annexure B"."

    **DEBUG YOUR SEARCH:**
    - Search entire document for "Annexure B"
    - Note exactly where it appears (which paragraph/sub-paragraph)
    - Extract the complete reference (like "2(f)" if it's in paragraph 2, sub-point f)

    **CRITICAL OUTPUT FORMAT - RETURN EXACTLY THESE LINES:**

    Annexure_B_Reference_Found: [Yes/No]
    Annexure_B_Paragraph_Number: [Complete reference like "2(f)" or "Not Found"]
    Full_Reference_Text: [Complete text of paragraph mentioning Annexure B]
    Section_Found: [Yes/No]
    Contains_Annexure_B: [Yes/No]
    Contains_Internal_Controls: [Yes/No]
    Debug_Annexure_B_Location: [Exact location where "Annexure B" was found]
    Debug_Full_Paragraph_Context: [The complete paragraph text where Annexure B appears]

    **SEARCH REQUIREMENTS:**
    - Be case-insensitive in your search
    - Look for "Annexure B" with or without quotes
    - Include the complete paragraph reference with sub-points (like "2(f)")
    - Look in the "Report on Other Legal and Regulatory Requirements" section

    **EXAMPLE SUCCESSFUL RESPONSE:**
    Annexure_B_Reference_Found: Yes
    Annexure_B_Paragraph_Number: 2(f)
    Full_Reference_Text: With respect to adequacy of the internal financial controls over financial statements of the Holding Company and its subsidiary companies which are incorporated in India and the operating effectiveness of such controls, refer to our separate Report in "Annexure B".
    Section_Found: Yes
    Contains_Annexure_B: Yes
    Contains_Internal_Controls: Yes
    Debug_Annexure_B_Location: Paragraph 2, sub-point f
    Debug_Full_Paragraph_Context: f) With respect to adequacy of the internal financial controls over financial statements of the Holding Company and its subsidiary companies which are incorporated in India and the operating effectiveness of such controls, refer to our separate Report in "Annexure B". Our report expresses an unmodified opinion on the adequacy and operating effectiveness of the internal financial control over financial reporting of those companies, for reasons stated therein.
  `,
  variables: []
},
// Annexure A Back-Reference Extraction - ENHANCED
annexure_a_reference_back_extract: {
  key: 'annexure_a_reference_back_extract',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Annexure A PDF to extract the back-reference to the Audit Report paragraph:

    **TASK:**
    1. Look at the very beginning/header of the Annexure A document
    2. Find text that contains "Referred to in paragraph" or similar reference pattern
    3. Extract the paragraph number from this reference

    **SEARCH PATTERNS TO LOOK FOR:**
    - "Referred to in paragraph"
    - "Referred to in para"
    - "paragraph [number]"
    - "Report on Other Legal and Regulatory Requirements"
    - Look for any back-reference pattern at the start of the document

    **EXAMPLE TEXT TO MATCH:**
    "Referred to in paragraph 1 on 'Report on Other Legal and Regulatory Requirements' of our report of even date to the members of TVS Srichakra Limited"

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Back_Reference_Found: [Yes/No]
    Back_Reference_Paragraph: [Number only, e.g., "1" or "Not Found"]
    Full_Reference_Text: [Complete back-reference sentence from the header]
    Contains_Referred_Pattern: [Yes if "Referred to" pattern found, No if not]
    Contains_Legal_Requirements: [Yes if "Report on Other Legal and Regulatory Requirements" found, No if not]

    **SEARCH INSTRUCTIONS:**
    1. Look at the first few lines/paragraphs of the Annexure A document
    2. Search for any text containing "Referred to" (case insensitive)
    3. Extract the paragraph number mentioned after "paragraph"
    4. Extract only the number (e.g., "1" from "paragraph 1")

    **EXAMPLE RESPONSE:**
    Back_Reference_Found: Yes
    Back_Reference_Paragraph: 1
    Full_Reference_Text: Referred to in paragraph 1 on 'Report on Other Legal and Regulatory Requirements' of our report of even date to the members of TVS Srichakra Limited ("the Company") on the standalone financial statements as of and for the year ended 31 March 2024.
    Contains_Referred_Pattern: Yes
    Contains_Legal_Requirements: Yes
  `,
  variables: []
},

// Annexure B Back-Reference Extraction - ENHANCED
annexure_b_reference_back_extract: {
  key: 'annexure_b_reference_back_extract',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Annexure B PDF to extract the back-reference to the Audit Report paragraph:

    **TASK:**
    1. Look at the very beginning/header of the Annexure B document
    2. Find text that contains "Referred to in paragraph" or similar reference pattern
    3. Extract the COMPLETE paragraph number including sub-points (e.g., "2(f)")

    **SEARCH PATTERNS TO LOOK FOR:**
    - "Referred to in paragraph"
    - "Referred to in para"
    - "paragraph [number with sub-points]" like "paragraph 2(f)"
    - "Report on Other Legal and Regulatory Requirements"
    - Look for any back-reference pattern at the start of the document

    **EXAMPLE TEXT TO MATCH:**
    "Referred to in paragraph 2(f) on 'Report on Other Legal and Regulatory Requirements' of our report of even date Report on the Internal Financial Controls"

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these lines, nothing else:

    Back_Reference_Found: [Yes/No]
    Back_Reference_Paragraph: [Complete reference with sub-points, e.g., "2(f)" or "Not Found"]
    Full_Reference_Text: [Complete back-reference sentence from the header]
    Contains_Referred_Pattern: [Yes if "Referred to" pattern found, No if not]
    Contains_Legal_Requirements: [Yes if "Report on Other Legal and Regulatory Requirements" found, No if not]

    **SEARCH INSTRUCTIONS:**
    1. Look at the first few lines/paragraphs of the Annexure B document
    2. Search for any text containing "Referred to" (case insensitive)
    3. Extract the complete paragraph reference mentioned after "paragraph"
    4. Include sub-points like letters in parentheses (e.g., "2(f)" not just "2")

    **EXAMPLE RESPONSE:**
    Back_Reference_Found: Yes
    Back_Reference_Paragraph: 2(f)
    Full_Reference_Text: Referred to in paragraph 2(f) on 'Report on Other Legal and Regulatory Requirements' of our report of even date Report on the Internal Financial Controls with reference to the aforesaid standalone financial statements under Clause (i) of Sub-section 3 of Section 143 of the Companies Act, 2013
    Contains_Referred_Pattern: Yes
    Contains_Legal_Requirements: Yes
  `,
  variables: []
},


// multiDocumentPrompts.ts - NEW DIRECT SEARCH PROMPTS

// Add these to your MULTI_DOCUMENT_PROMPTS object:

// 1. DIRECT FIXED DEPOSITS ANALYSIS (replaces balance sheet + notes approach)
notes_direct_borrowings_fixed_deposits_analysis: {
  key: 'notes_direct_borrowings_fixed_deposits_analysis',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF for Fixed Deposits in borrowings WITHOUT depending on Balance Sheet:

    **TASK:**
    1. Look for ALL borrowings-related notes in the document (search for "Borrowings", "Loans", "Current Liabilities", "Non-Current Liabilities")
    2. In ANY borrowings note, check if "Fixed Deposits" are mentioned as part of borrowings
    3. Search comprehensively across ALL notes for borrowings with fixed deposits

    **COMPREHENSIVE SEARCH PATTERNS:**
    - "Fixed deposits from public"
    - "Fixed deposits from directors"
    - "Fixed deposits" under borrowings
    - "Deposits" in borrowings context
    - "Public deposits"
    - "Term deposits"
    - "Borrowings and deposits"

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 5 lines, nothing else:

    Borrowings_Notes_Found: [Yes/No]
    Current_Borrowings_Has_Fixed_Deposits: [Yes/No/Not Found]
    Non_Current_Borrowings_Has_Fixed_Deposits: [Yes/No/Not Found]
    Fixed_Deposits_Details_Present: [Yes/No]
    Overall_Fixed_Deposits_In_Borrowings: [Yes/No]

    **EVALUATION RULES:**
    - Borrowings_Notes_Found: "Yes" if any borrowings-related notes exist
    - Current_Borrowings_Has_Fixed_Deposits: "Yes" if current borrowings mention fixed deposits
    - Non_Current_Borrowings_Has_Fixed_Deposits: "Yes" if non-current borrowings mention fixed deposits
    - Fixed_Deposits_Details_Present: "Yes" if either current or non-current has fixed deposits
    - Overall_Fixed_Deposits_In_Borrowings: "Yes" if any fixed deposits found in any borrowings

    **EXAMPLE RESPONSE:**
    Borrowings_Notes_Found: Yes
    Current_Borrowings_Has_Fixed_Deposits: Yes
    Non_Current_Borrowings_Has_Fixed_Deposits: No
    Fixed_Deposits_Details_Present: Yes
    Overall_Fixed_Deposits_In_Borrowings: Yes
  `,
  variables: []
},

notes_direct_goods_in_transit_analysis: {
  key: 'notes_direct_goods_in_transit_analysis',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF for Goods in Transit WITHOUT any Balance Sheet reference:

    **TASK:**
    1. Search THROUGHOUT the entire Notes to Accounts document
    2. Look for ANY mention of "Goods in Transit" in ANY note
    3. Check inventory-related notes, working capital notes, current assets notes
    4. Do NOT look for specific note numbers - search the ENTIRE document

    **COMPREHENSIVE SEARCH PATTERNS:**
    - "Goods in Transit"
    - "Goods in-transit"
    - "Stock in transit"
    - "Material in transit"
    - "Inventory in transit"
    - "Goods under transit"
    - "Transit inventory"
    - Any goods/materials/stock mentioned as being "in transit"

    **SEARCH LOCATIONS:**
    - Inventory composition notes
    - Current assets breakdown
    - Working capital analysis
    - Cost of goods sold notes
    - Any note mentioning inventory components

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 4 lines, nothing else:

    Inventory_Notes_Found: [Yes/No]
    Goods_In_Transit_Present: [Yes/No]
    Goods_In_Transit_Details: [Brief description or "Not Found"]
    Goods_In_Transit_Amount: [Amount if specified or "Not Specified"]

    **EVALUATION RULES:**
    - Inventory_Notes_Found: "Yes" if ANY inventory-related content exists anywhere
    - Goods_In_Transit_Present: "Yes" if "Goods in Transit" mentioned ANYWHERE in the document
    - Goods_In_Transit_Details: Brief description of what was found
    - Goods_In_Transit_Amount: Extract amount if mentioned

    **EXAMPLE RESPONSE IF GOODS IN TRANSIT FOUND:**
    Inventory_Notes_Found: Yes
    Goods_In_Transit_Present: Yes
    Goods_In_Transit_Details: Goods in transit included in inventory valuation
    Goods_In_Transit_Amount: 50.00 Lakhs

    **EXAMPLE RESPONSE IF NO GOODS IN TRANSIT:**
    Inventory_Notes_Found: Yes
    Goods_In_Transit_Present: No
    Goods_In_Transit_Details: Not Found
    Goods_In_Transit_Amount: Not Specified
  `,
  variables: []
},
// 3. DIRECT INVENTORY WRITE-OFF ANALYSIS (no balance sheet dependency)
notes_direct_inventory_writeoff_analysis: {
  key: 'notes_direct_inventory_writeoff_analysis',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF for Inventory Write-offs WITHOUT depending on Balance Sheet:

    **TASK:**
    1. Search ALL notes for inventory-related content and write-off information
    2. Look for any mention of inventory write-offs, obsolescence, or discrepancies
    3. Check P&L notes for inventory write-off expenses

    **COMPREHENSIVE SEARCH PATTERNS:**
    - "Inventory write-off"
    - "Write off of inventory"
    - "Inventory obsolescence"
    - "Stock write-off"
    - "Inventory discrepancies"
    - "Provision for obsolete inventory"
    - "Inventory losses"

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 4 lines, nothing else:

    Inventory_Notes_Found: [Yes/No]
    Inventory_Writeoff_Present: [Yes/No]
    Writeoff_Details: [Brief description or "Not Found"]
    Writeoff_Amount: [Amount if specified or "Not Specified"]

    **EVALUATION RULES:**
    - Inventory_Notes_Found: "Yes" if any inventory-related notes exist
    - Inventory_Writeoff_Present: "Yes" if write-off mentioned anywhere
    - Writeoff_Details: Brief description of write-off if found
    - Writeoff_Amount: Extract amount if specified

    **EXAMPLE RESPONSE:**
    Inventory_Notes_Found: Yes
    Inventory_Writeoff_Present: Yes
    Writeoff_Details: Inventory write-off of obsolete stock during the year
    Writeoff_Amount: 25.00 Lakhs
  `,
  variables: []
},

// 4. DIRECT SECURED BORROWINGS ANALYSIS (no balance sheet dependency)
notes_direct_secured_borrowings_analysis: {
  key: 'notes_direct_secured_borrowings_analysis',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF for Secured Borrowings WITHOUT depending on Balance Sheet:

    **TASK:**
    1. Search ALL notes for borrowings information
    2. Look specifically for "Secured Borrowings" or "Secured Loans"
    3. Extract amounts and check if > 5 crores threshold

    **COMPREHENSIVE SEARCH PATTERNS:**
    - "Secured borrowings"
    - "Secured loans"
    - "Term loans secured by"
    - "Working capital secured by"
    - "Bank borrowings secured"
    - Any loans mentioned as secured by assets

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 5 lines, nothing else:

    Borrowings_Notes_Found: [Yes/No]
    Secured_Borrowings_Present: [Yes/No]
    Secured_Borrowings_Amount: [Amount in crores or "Not Found"]
    Amount_More_Than_5_Crores: [Yes/No]
    Security_Details: [Brief description of security or "Not Specified"]

    **EVALUATION RULES:**
    - Borrowings_Notes_Found: "Yes" if any borrowings notes exist
    - Secured_Borrowings_Present: "Yes" if secured borrowings mentioned
    - Secured_Borrowings_Amount: Extract amount and convert to crores
    - Amount_More_Than_5_Crores: "Yes" if amount > 5 crores, "No" if ≤ 5 crores
    - Security_Details: Brief description of what secures the borrowings

    **EXAMPLE RESPONSE:**
    Borrowings_Notes_Found: Yes
    Secured_Borrowings_Present: Yes
    Secured_Borrowings_Amount: 15.5
    Amount_More_Than_5_Crores: Yes
    Security_Details: Secured by hypothecation of current assets and plant & machinery
  `,
  variables: []
},

notes_secured_borrowings_analysis: {
  key: 'notes_secured_borrowings_analysis',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this Notes to Accounts PDF for Secured Borrowings in Current Liabilities:

    **TASK:**
    1. Search for "Current Liabilities" or "Other Current Liabilities" notes
    2. Look specifically for "Secured Borrowings" or "Secured Loans"
    3. Extract the amount and check if > 5 crores

    **SEARCH PATTERNS:**
    - "Secured borrowings"
    - "Secured loans"
    - "Term loans secured by"
    - "Working capital secured by"
    - "Bank borrowings secured"
    - Any borrowings mentioned as secured by assets

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 5 lines, nothing else:

    Current_Liabilities_Note_Found: [Yes/No]
    Secured_Borrowings_Present: [Yes/No]
    Secured_Borrowings_Amount: [Amount in crores or 0]
    Amount_More_Than_5_Crores: [Yes/No]
    Security_Details: [Brief description or "Not Specified"]

    **EVALUATION RULES:**
    - Current_Liabilities_Note_Found: "Yes" if current liabilities notes exist
    - Secured_Borrowings_Present: "Yes" if secured borrowings mentioned
    - Secured_Borrowings_Amount: Extract amount and convert to crores
    - Amount_More_Than_5_Crores: "Yes" if amount > 5 crores
    - Security_Details: What secures the borrowings

    **EXAMPLE RESPONSE:**
    Current_Liabilities_Note_Found: Yes
    Secured_Borrowings_Present: Yes
    Secured_Borrowings_Amount: 15.5
    Amount_More_Than_5_Crores: Yes
    Security_Details: Secured by hypothecation of current assets
  `,
  variables: []
},

// CARO Clause (ii)(b) Quarterly Returns Check
caro_clause_ii_b_quarterly_returns_check: {
  key: 'caro_clause_ii_b_quarterly_returns_check',
  template: `
    🔍 CRITICAL: You MUST return EXACTLY the format shown below. No other format will work.

    Analyze this CARO Annexure PDF for clause (ii)(b) quarterly returns disclosure:

    **TASK:**
    Look for clause (ii)(b) and check if it mentions quarterly returns to banks/financial institutions.

    **SEARCH PATTERNS:**
    - "quarterly returns"
    - "banks and financial institutions"
    - "agreement of inventory with quarterly returns"
    - "submitted to banks"
    - "returns filed with banks"

    **CRITICAL OUTPUT FORMAT - YOU MUST FOLLOW THIS EXACTLY:**
    Return ONLY these 4 lines, nothing else:

    Clause_II_B_Present: [Yes/No]
    Mentions_Quarterly_Returns: [Yes/No]
    Mentions_Banks_Financial_Institutions: [Yes/No]
    Has_Quarterly_Returns_Disclosure: [Yes/No]

    **EVALUATION RULES:**
    - Clause_II_B_Present: "Yes" if clause (ii)(b) exists
    - Mentions_Quarterly_Returns: "Yes" if "quarterly returns" mentioned
    - Mentions_Banks_Financial_Institutions: "Yes" if banks mentioned
    - Has_Quarterly_Returns_Disclosure: "Yes" if both returns and banks mentioned

    **EXAMPLE RESPONSE:**
    Clause_II_B_Present: Yes
    Mentions_Quarterly_Returns: Yes
    Mentions_Banks_Financial_Institutions: Yes
    Has_Quarterly_Returns_Disclosure: Yes
  `,
  variables: []
},

};




// ===========================================
// INTEGRATION FUNCTION
// ===========================================

/**
 * Add multi-document prompts to existing prompt templates
 */
export function addMultiDocumentPrompts(existingPrompts: Record<string, any>): Record<string, any> {
  console.log(`Adding ${Object.keys(MULTI_DOCUMENT_PROMPTS).length} multi-document prompts to existing ${Object.keys(existingPrompts).length} prompts`);

  return {
    ...existingPrompts,
    ...MULTI_DOCUMENT_PROMPTS
  };
}

/**
 * Get all multi-document prompt keys
 */
export function getMultiDocumentPromptKeys(): string[] {
  return Object.keys(MULTI_DOCUMENT_PROMPTS);
}

/**
 * Validate that all required prompts exist for multi-document checks
 */
export function validateMultiDocumentPrompts(allPrompts: Record<string, any>): {
  isValid: boolean;
  missingPrompts: string[];
} {
  const requiredPrompts = Object.keys(MULTI_DOCUMENT_PROMPTS);
  const missingPrompts = requiredPrompts.filter(key => !allPrompts[key]);

  return {
    isValid: missingPrompts.length === 0,
    missingPrompts
  };
}